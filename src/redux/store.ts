/*
Copyright (c) 2025-present Navicater Solutions

This source code is licensed under the license found in the
LICENSE file in the root directory of this source tree.
*/
import { persistReducer, persistStore } from 'redux-persist';
import AsyncStorage from '@react-native-async-storage/async-storage';
import { configureStore } from '@reduxjs/toolkit';
import aboutReducer from './slices/about/aboutSlice';
import aiChatReducer from './slices/aichat/aiChatSlice';
import contentReducer from './slices/content/contentSlice';
import searchReducer from './slices/entitysearch/searchSlice';
import experienceReducer from './slices/experience/experienceSlice';
import globalSearchReducer from './slices/globalsearch/globalSearchSlice';
import userReducer from './slices/user/userSlice';

const userPersistConfig = {
  key: 'user',
  storage: AsyncStorage,
};

const globalSearchPersistConfig = {
  key: 'globalsearch',
  storage: AsyncStorage,
};

const aiChatPersistConfig = {
  key: 'aichat',
  storage: AsyncStorage,
};

const persistedUserReducer = persistReducer(userPersistConfig, userReducer);
const persistedGlobalSearchReducer = persistReducer(globalSearchPersistConfig, globalSearchReducer);
const persistedAIChatReducer = persistReducer(aiChatPersistConfig, aiChatReducer);

export const store = configureStore({
  reducer: {
    user: persistedUserReducer,
    globalsearch: persistedGlobalSearchReducer,
    aichat: persistedAIChatReducer,
    search: searchReducer,
    content: contentReducer,
    experience: experienceReducer,
    about: aboutReducer,
  },
  middleware: (getDefaultMiddleware) =>
    getDefaultMiddleware({
      serializableCheck: false,
    }),
});

export const persistor = persistStore(store);
export type RootState = ReturnType<typeof store.getState>;
export type AppDispatch = typeof store.dispatch;
