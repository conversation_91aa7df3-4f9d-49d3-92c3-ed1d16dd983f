import { createSlice, createAsyncThunk, type PayloadAction } from '@reduxjs/toolkit';
import { fetchCommentsAPI, fetchCommentRepliesAPI } from '@/src/networks/content/comment';
import {
  addPostAPI,
  fetchProfilePostsAPI,
  fetchPostsAPI,
  fetchPostAPI,
  editPostAPI,
} from '@/src/networks/content/post';
import { fetchReactionsAPI } from '@/src/networks/content/reaction';
import type {
  PostCreateOneI,
  ReactionFetchManyI,
  GetCommentsPayloadI,
  PostExternalClientI,
  GetPostsPayloadI,
} from '@/src/networks/content/types';
import {
  createScrapbookPost as createScrapbookPostAPI,
  fetchScrapbookPosts as fetchScrapbookPostsAPI,
  editScrapbookPost as editScrapbookPostAPI,
  fetchScrapbookPost,
  fetchScrapbookComments as fetchScrapbookCommentsAPI,
  fetchScrapbookCommentReplies as fetchScrapbookCommentRepliesAPI,
  fetchScrapbookReactions as fetchScrapbookReactionsAPI,
} from '@/src/networks/port/scrapbook';
import type {
  ScrapbookPostCreateBodyI,
  ScrapBookPostFetchForClientI,
  ScrapBookCommentFetchManyQueryI,
  ScrapBookCommentFetchForClientI,
} from '@/src/networks/port/types';
import { selectCurrentUser } from '../../selectors/user';
import type { RootState } from '../../store';
import type {
  ContentState,
  CommentPayload,
  CommentActionPayload,
  DeletePostPayload,
  ReactionPayload,
  RevertDeletePayload,
  FetchRepliesPayload,
  ScrapbookCommentPayload,
  ScrapbookCommentActionPayload,
  FetchScrapbookRepliesPayload,
  UserI,
  ExtendedPostI,
} from './types';

const initialState: ContentState = {
  posts: [],
  post: null,
  pagination: {
    cursorId: null,
    otherCursorId: undefined,
    hasMore: true,
  },
  ownPosts: [],
  ownPostsPagination: {
    cursorId: null,
    hasMore: true,
  },
  profilePosts: {},
  reactions: {},
  comments: {},
  commentReplies: {},
  deletedPosts: {},
  deletedScrapbookPosts: {},
  scrapbookPosts: [],
  scrapbookPagination: {
    page: 0,
    hasMore: true,
    total: 0,
  },
  scrapbookComments: {},
  scrapbookCommentReplies: {},
  searchPosts: {},
  scrapbookReactions: {},
};

const generateTempId = () => `temp_${Date.now()}_${Math.random().toString(36).substring(2, 11)}`;

const updatePostInAllCollections = (
  state: ContentState,
  postId: string,
  updater: (post: PostExternalClientI) => void,
) => {
  const postIndex = state.posts.findIndex((p) => p.id === postId);
  if (postIndex !== -1) {
    updater(state.posts[postIndex]);
  }

  const ownPostIndex = state.ownPosts.findIndex((p) => p.id === postId);
  if (ownPostIndex !== -1) {
    updater(state.ownPosts[ownPostIndex]);
  }

  Object.keys(state.profilePosts).forEach((profileId) => {
    const profilePostIndex = state.profilePosts[profileId].posts.findIndex((p) => p.id === postId);
    if (profilePostIndex !== -1) {
      updater(state.profilePosts[profileId].posts[profilePostIndex]);
    }
  });

  Object.keys(state.searchPosts).forEach((searchTerm) => {
    const searchPostIndex = state.searchPosts[searchTerm].findIndex((p) => p.id === postId);
    if (searchPostIndex !== -1) {
      updater(state.searchPosts[searchTerm][searchPostIndex]);
    }
  });

  if (state.post?.id === postId && !state.post.isScrapbookPost) {
    updater(state.post);
  }
};

const updateScrapbookPostInAllCollections = (
  state: ContentState,
  postId: string,
  updater: (post: ScrapBookPostFetchForClientI | ExtendedPostI) => void,
) => {
  const post = state.scrapbookPosts.find((p) => p.id === postId);
  if (post) updater(post);

  if (state.post?.id === postId && state.post.isScrapbookPost) {
    updater(state.post as ExtendedPostI);
  }
};

const addPostToCollections = (
  state: ContentState,
  post: PostExternalClientI,
  currentUser?: UserI,
) => {
  const existsInPosts = state.posts.some((p) => p.id === post.id);
  if (!existsInPosts) {
    state.posts.unshift(post);
  }

  if (currentUser && post.Profile.id === currentUser.profileId) {
    const existsInOwnPosts = state.ownPosts.some((p) => p.id === post.id);
    if (!existsInOwnPosts) {
      state.ownPosts.unshift(post);
    }
  }

  if (post.Profile.id) {
    if (!state.profilePosts[post.Profile.id]) {
      state.profilePosts[post.Profile.id] = {
        posts: [],
        pagination: { cursorId: null, hasMore: true },
      };
    }
    const existsInProfile = state.profilePosts[post.Profile.id].posts.some((p) => p.id === post.id);
    if (!existsInProfile) {
      state.profilePosts[post.Profile.id].posts.unshift(post);
    }
  }
};

const removePostFromCollections = (state: ContentState, postId: string) => {
  state.posts = state.posts.filter((p) => p.id !== postId);
  state.ownPosts = state.ownPosts.filter((p) => p.id !== postId);

  Object.keys(state.profilePosts).forEach((profileId) => {
    if (state.profilePosts[profileId]) {
      state.profilePosts[profileId].posts = state.profilePosts[profileId].posts.filter(
        (p) => p.id !== postId,
      );
    }
  });

  Object.keys(state.searchPosts).forEach((searchTerm) => {
    state.searchPosts[searchTerm] = state.searchPosts[searchTerm].filter((p) => p.id !== postId);
  });

  if (state.post?.id === postId) {
    state.post = null;
  }
};

const createOptimisticComment = (user: UserI, text: string, tempId: string) => ({
  id: tempId,
  text,
  createdAt: new Date().toISOString(),
  updatedAt: new Date().toISOString(),
  Profile: {
    id: user.profileId,
    name: user.fullName,
    avatar: user.avatar,
    designation: user.designation,
    entity: user.organisation,
  },
  cursorId: null,
  replies: [],
  repliesCount: 0,
});

const createOptimisticScrapbookComment = (
  user: UserI,
  text: string,
  tempId: string,
): ScrapBookCommentFetchForClientI => ({
  id: tempId,
  text,
  cursorId: null,
  createdAt: new Date(),
  updatedAt: new Date(),
  repliesCount: 0,
  Profile: {
    id: user.profileId,
    name: user.fullName,
    avatar: user.avatar,
    designation: user.designation
      ? {
          id: user.designation.id,
          name: user.designation.name,
          dataType: user.designation.dataType,
        }
      : null,
    entity: user.organisation
      ? {
          id: user.organisation.id,
          name: user.organisation.name,
          dataType: user.organisation.dataType,
        }
      : null,
  },
  replies: [],
});

export const fetchPosts = createAsyncThunk(
  'content/fetchPosts',
  async (
    { pageSize = '10', refresh = false }: { pageSize?: string; refresh?: boolean },
    { getState, rejectWithValue },
  ) => {
    try {
      const state = getState() as RootState;
      const { pagination } = state.content;
      const response = await fetchPostsAPI({
        pageSize,
        cursorId: refresh ? null : (pagination.cursorId?.toString() ?? null),
        otherCursorId: refresh ? undefined : pagination.otherCursorId?.toString(),
      });
      return { response, refresh };
    } catch (error) {
      return rejectWithValue('Failed to fetch posts');
    }
  },
);

export const fetchPost = createAsyncThunk(
  'content/fetchSinglePost',
  async ({ id, type }: { id: string; type?: string }, { rejectWithValue }) => {
    try {
      let postData: ExtendedPostI;

      if (type === 'SCRAPBOOK_POST') {
        const scrapbookPostData = await fetchScrapbookPost(id);
        postData = {
          id: scrapbookPostData.id,
          caption: scrapbookPostData.textPreview || '',
          createdAt:
            typeof scrapbookPostData.createdAt === 'string'
              ? scrapbookPostData.createdAt
              : scrapbookPostData.createdAt.toISOString(),
          reactionsCount: scrapbookPostData.reactionCount || 0,
          totalCommentsCount: scrapbookPostData.commentCount || 0,
          isLiked: scrapbookPostData.isLiked,
          Profile: {
            id: scrapbookPostData.profile.id,
            name: scrapbookPostData.profile.name || 'Unknown User',
            avatar: scrapbookPostData.profile.avatar,
            designation: scrapbookPostData.profile.designationText
              ? {
                  name: scrapbookPostData.profile.designationText,
                  id: '',
                  dataType: 'master',
                }
              : null,
            entity: null,
          },
          Media: [],
          isScrapbookPost: true,
        };
      } else {
        const regularPostData = await fetchPostAPI(id);
        postData = {
          ...regularPostData,
          isScrapbookPost: false,
        };
      }

      return { post: postData, type };
    } catch (error) {
      return rejectWithValue('Failed to fetch post');
    }
  },
);

export const fetchProfilePosts = createAsyncThunk(
  'content/fetchProfilePosts',
  async (
    { profileId, pageSize = '10', refresh = false }: GetPostsPayloadI & { refresh?: boolean },
    { getState, rejectWithValue },
  ) => {
    try {
      const state = getState() as RootState;
      const currentUser = selectCurrentUser(state);
      const isOwnProfile = profileId === currentUser.profileId;
      const profilePagination = isOwnProfile
        ? state.content.ownPostsPagination
        : state.content.profilePosts[profileId as string]?.pagination;

      const response = await fetchProfilePostsAPI({
        profileId,
        pageSize,
        cursorId: refresh ? null : (profilePagination?.cursorId?.toString() ?? null),
      });

      return { profileId, response, refresh, globalState: state };
    } catch (error) {
      return rejectWithValue('Failed to fetch profile posts');
    }
  },
);

export const fetchScrapbookPosts = createAsyncThunk(
  'content/fetchScrapbookPosts',
  async (
    params: {
      portUnLocode: string;
      page?: number;
      pageSize?: number;
      refresh?: boolean;
    },
    { getState, rejectWithValue },
  ) => {
    try {
      const { portUnLocode, page = 1, pageSize = 10, refresh = false } = params;
      const state = getState() as RootState;

      let currentPage = page;
      if (!refresh) {
        const existingPagination = state.content.scrapbookPagination;
        currentPage = existingPagination ? existingPagination.page + 1 : 1;
      } else {
        currentPage = 1;
      }

      const response = await fetchScrapbookPostsAPI({
        portUnLocode,
        page: currentPage,
        pageSize,
      });

      return { response, refresh, page: currentPage, portUnLocode, pageSize };
    } catch (error) {
      return rejectWithValue('Failed to fetch scrapbook posts');
    }
  },
);

export const fetchScrapbookCommentsForPost = createAsyncThunk(
  'content/fetchScrapbookCommentsForPost',
  async (query: ScrapBookCommentFetchManyQueryI, { rejectWithValue }) => {
    try {
      const response = await fetchScrapbookCommentsAPI(query);
      return {
        scrapbookPostId: query.scrapBookPostId,
        comments: response,
        isInitialFetch: !query.cursorId,
      };
    } catch (error) {
      return rejectWithValue('Failed to fetch scrapbook comments');
    }
  },
);

export const fetchScrapbookCommentReplies = createAsyncThunk(
  'content/fetchScrapbookCommentReplies',
  async (query: FetchScrapbookRepliesPayload, { rejectWithValue }) => {
    try {
      const response = await fetchScrapbookCommentRepliesAPI({
        scrapBookPostId: query.scrapbookPostId,
        parentCommentId: query.parentCommentId,
        cursorId: query.cursorId,
        pageSize: query.pageSize || 10,
      });
      return {
        parentCommentId: query.parentCommentId,
        replies: response,
        isInitialFetch: !query.cursorId,
      };
    } catch (error) {
      return rejectWithValue('Failed to fetch scrapbook comment replies');
    }
  },
);

export const fetchScrapbookReactionsForPost = createAsyncThunk(
  'content/fetchScrapbookReactionsForPost',
  async (query: { postId: string; page: number; pageSize: number }, { rejectWithValue }) => {
    try {
      const response = await fetchScrapbookReactionsAPI({
        scrapBookPostId: query.postId,
        page: query.page,
        pageSize: query.pageSize,
      });
      return {
        postId: query.postId,
        reactions: response.reactions,
        totalCount: response.totalCount,
      };
    } catch (error) {
      return rejectWithValue('Failed to fetch scrapbook reactions');
    }
  },
);

export const createPost = createAsyncThunk(
  'content/createPost',
  async (payload: PostCreateOneI, { rejectWithValue, getState, dispatch }) => {
    try {
      const state = getState() as RootState;
      const currentUser = selectCurrentUser(state);
      const tempId = generateTempId();

      if (!currentUser) {
        return rejectWithValue('User not authenticated');
      }

      const tempPost: PostExternalClientI = {
        id: tempId,
        caption: payload.caption || '',
        reactionsCount: 0,
        totalCommentsCount: 0,
        createdAt: new Date().toISOString(),
        Media: payload.files || [],
        isLiked: false,
        Profile: {
          id: currentUser.profileId,
          name: currentUser.fullName,
          avatar: currentUser.avatar,
          entity: currentUser.organisation || null,
          designation: currentUser.designation || null,
        },
      };

      dispatch(addPostOptimistic({ post: tempPost, tempId }));

      try {
        const response = await addPostAPI(payload);
        return { response, currentUser, tempId };
      } catch (error) {
        dispatch(removePostOptimistic({ tempId }));
        throw error;
      }
    } catch (error) {
      return rejectWithValue('Failed to create post');
    }
  },
);

export const editPost = createAsyncThunk(
  'content/editPost',
  async (
    { payload, postId }: { payload: PostCreateOneI; postId: string },
    { rejectWithValue, getState },
  ) => {
    try {
      const response = await editPostAPI(payload, postId);
      const state = getState() as RootState;
      const currentUser = selectCurrentUser(state);

      return { response, currentUser, postId };
    } catch (error) {
      return rejectWithValue('Failed to edit post');
    }
  },
);

export const createScrapbookPost = createAsyncThunk(
  'content/createScrapbookPost',
  async (payload: ScrapbookPostCreateBodyI, { rejectWithValue, getState, dispatch }) => {
    try {
      const state = getState() as RootState;
      const currentUser = selectCurrentUser(state);
      const tempId = generateTempId();

      if (!currentUser) {
        return rejectWithValue('User not authenticated');
      }

      const tempPost: ScrapBookPostFetchForClientI = {
        id: tempId,
        textPreview: payload.text.trim().slice(0, 120),
        createdAt: new Date().toISOString(),
        reactionCount: 0,
        commentCount: 0,
        isLiked: false,
        profile: {
          id: currentUser.profileId,
          name: currentUser.fullName,
          avatar: currentUser.avatar,
          designationText: currentUser.designation?.name || '',
        },
      };

      dispatch(addScrapbookPostOptimistic({ post: tempPost, tempId }));

      try {
        const response = await createScrapbookPostAPI(payload);
        return { response, currentUser, portUnLocode: payload.portUnLocode, tempId };
      } catch (error) {
        dispatch(removeScrapbookPostOptimistic({ tempId }));
        throw error;
      }
    } catch (error) {
      return rejectWithValue('Failed to create scrapbook post');
    }
  },
);

export const editScrapbookPost = createAsyncThunk(
  'content/editScrapbookPost',
  async (
    { payload, postId }: { payload: ScrapbookPostCreateBodyI; postId: string },
    { rejectWithValue, getState },
  ) => {
    try {
      const response = await editScrapbookPostAPI(postId, payload);
      const state = getState() as RootState;
      const currentUser = selectCurrentUser(state);

      return { response, currentUser, postId, payload };
    } catch (error) {
      return rejectWithValue('Failed to edit scrapbook post');
    }
  },
);

export const fetchReactionsForPost = createAsyncThunk(
  'content/fetchReactionsForPost',
  async (query: ReactionFetchManyI, { rejectWithValue }) => {
    try {
      const { reactions, totalCount } = await fetchReactionsAPI(query);
      return { postId: query.postId, reactions, totalCount };
    } catch (error) {
      return rejectWithValue('Failed to fetch reactions');
    }
  },
);

export const fetchCommentsForPost = createAsyncThunk(
  'content/fetchCommentsForPost',
  async (query: GetCommentsPayloadI, { rejectWithValue }) => {
    try {
      const response = await fetchCommentsAPI(query);
      return { postId: query.postId, comments: response, isInitialFetch: !query.cursorId };
    } catch (error) {
      return rejectWithValue('Failed to fetch comments');
    }
  },
);

export const fetchCommentReplies = createAsyncThunk(
  'content/fetchCommentReplies',
  async (query: FetchRepliesPayload, { rejectWithValue }) => {
    try {
      const response = await fetchCommentRepliesAPI({
        postId: query.postId,
        parentCommentId: query.parentCommentId,
        cursorId: query.cursorId,
        pageSize: query.pageSize || 10,
      });
      return {
        parentCommentId: query.parentCommentId,
        replies: response,
        isInitialFetch: !query.cursorId,
      };
    } catch (error) {
      return rejectWithValue('Failed to fetch comment replies');
    }
  },
);

const contentSlice = createSlice({
  name: 'content',
  initialState,
  reducers: {
    resetContentState: () => initialState,
    resetScrapbookState: (state) => {
      state.scrapbookPosts = [];
      state.scrapbookPagination = { page: 0, hasMore: true, total: 0 };
      state.scrapbookComments = {};
      state.scrapbookCommentReplies = {};
    },

    resetSearchPosts: (state) => {
      state.searchPosts = {};
    },

    setSearchPosts: (
      state,
      action: PayloadAction<{ searchTerm: string; posts: PostExternalClientI[]; append?: boolean }>,
    ) => {
      const { searchTerm, posts, append = false } = action.payload;

      const normalizedPosts = posts.map((post) => ({
        ...post,
        reactionsCount: post.reactionsCount || 0,
        totalCommentsCount: post.totalCommentsCount || 0,
      }));

      if (append && state.searchPosts[searchTerm]) {
        const existingIds = new Set(state.searchPosts[searchTerm].map((post) => post.id));
        const newPosts = normalizedPosts.filter((post) => !existingIds.has(post.id));
        state.searchPosts[searchTerm].push(...newPosts);
      } else {
        state.searchPosts[searchTerm] = normalizedPosts;
      }
    },

    addPostOptimistic: (
      state,
      action: PayloadAction<{ post: PostExternalClientI; tempId: string }>,
    ) => {
      const { post } = action.payload;
      addPostToCollections(state, post);
    },

    removePostOptimistic: (state, action: PayloadAction<{ tempId: string }>) => {
      const { tempId } = action.payload;
      removePostFromCollections(state, tempId);
    },

    updatePostWithRealId: (
      state,
      action: PayloadAction<{ tempId: string; realId: string; updatedPost: PostExternalClientI }>,
    ) => {
      const { tempId, realId, updatedPost } = action.payload;
      updatePostInAllCollections(state, tempId, (post) => {
        Object.assign(post, {
          id: realId,
          createdAt: updatedPost.createdAt || post.createdAt,
          caption: updatedPost.caption || post.caption,
          Media: updatedPost.Media || post.Media,
        });
      });
    },

    addScrapbookPostOptimistic: (
      state,
      action: PayloadAction<{ post: ScrapBookPostFetchForClientI; tempId: string }>,
    ) => {
      const { post } = action.payload;
      state.scrapbookPosts.unshift(post);
      state.scrapbookPagination.total += 1;
    },

    removeScrapbookPostOptimistic: (state, action: PayloadAction<{ tempId: string }>) => {
      const { tempId } = action.payload;
      state.scrapbookPosts = state.scrapbookPosts.filter((post) => post.id !== tempId);
      state.scrapbookPagination.total = Math.max(0, state.scrapbookPagination.total - 1);
    },

    updateScrapbookPostWithRealId: (
      state,
      action: PayloadAction<{
        tempId: string;
        realId: string;
        updatedPost: ScrapBookPostFetchForClientI;
      }>,
    ) => {
      const { tempId, realId, updatedPost } = action.payload;
      updateScrapbookPostInAllCollections(state, tempId, (post) => {
        Object.assign(post, {
          id: realId,
          createdAt: updatedPost.createdAt || post.createdAt,
          textPreview: updatedPost.textPreview || post.textPreview,
          reactionCount: updatedPost.reactionCount || post.reactionCount,
          commentCount: updatedPost.commentCount || post.commentCount,
        });
      });
    },

    addReactionOptimistc: (state, action: PayloadAction<ReactionPayload>) => {
      const { postId } = action.payload;
      updatePostInAllCollections(state, postId, (post) => {
        post.isLiked = true;
        post.reactionsCount = (post.reactionsCount || 0) + 1;
      });
    },

    removeReactionOptimistic: (state, action: PayloadAction<ReactionPayload>) => {
      const { postId } = action.payload;
      updatePostInAllCollections(state, postId, (post) => {
        post.isLiked = false;
        post.reactionsCount = Math.max(0, (post.reactionsCount || 0) - 1);
      });
    },

    addScrapbookReactionOptimistic: (state, action: PayloadAction<{ scrapbookPostId: string }>) => {
      const { scrapbookPostId } = action.payload;
      updateScrapbookPostInAllCollections(state, scrapbookPostId, (post) => {
        post.isLiked = true;
        post.reactionCount! += 1;
      });
    },

    removeScrapbookReactionOptimistic: (
      state,
      action: PayloadAction<{ scrapbookPostId: string }>,
    ) => {
      const { scrapbookPostId } = action.payload;
      updateScrapbookPostInAllCollections(state, scrapbookPostId, (post) => {
        post.isLiked = false;
        if ('reactionCount' in post) {
          post.reactionCount = post.reactionCount ? Math.max(0, post.reactionCount - 1) : 0;
        } else {
          post.reactionsCount = Math.max(0, post.reactionsCount - 1);
        }
      });
    },

    deletePostOptimistic: (state, action: PayloadAction<DeletePostPayload>) => {
      const { post } = action.payload;
      const postId = post.id;

      state.deletedPosts[postId] = {
        post,
        reactions: state.reactions[postId] || { reactions: [], totalCount: 0 },
        comments: state.comments[postId] || { comments: [] },
      };

      removePostFromCollections(state, postId);
      delete state.reactions[postId];
      delete state.comments[postId];
    },

    deleteScrapbookPostOptimistic: (state, action: PayloadAction<{ scrapbookPostId: string }>) => {
      const { scrapbookPostId } = action.payload;
      const deletedPost = state.scrapbookPosts.find((p) => p.id === scrapbookPostId);

      if (deletedPost) {
        state.deletedScrapbookPosts[scrapbookPostId] = { post: deletedPost };
      }

      state.scrapbookPosts = state.scrapbookPosts.filter((p) => p.id !== scrapbookPostId);
      delete state.scrapbookComments[scrapbookPostId];

      if (state.post?.id === scrapbookPostId && state.post.isScrapbookPost) {
        state.post = null;
      }
    },

    revertDeleteScrapbookPostOptimistic: (state, action: PayloadAction<RevertDeletePayload>) => {
      const { postId } = action.payload;
      const deletedData = state.deletedScrapbookPosts[postId];

      if (deletedData) {
        state.scrapbookPosts.unshift(deletedData.post);
        state.scrapbookPagination.total += 1;
        delete state.deletedScrapbookPosts[postId];
      }
    },

    revertDeletePostOptimistic: (state, action: PayloadAction<RevertDeletePayload>) => {
      const { postId } = action.payload;
      const deletedData = state.deletedPosts[postId];

      if (deletedData) {
        const { post, reactions, comments } = deletedData;
        addPostToCollections(state, post);

        if (reactions.reactions.length > 0) {
          state.reactions[postId] = reactions;
        }
        if (comments.comments?.length > 0) {
          state.comments[postId] = comments;
        }

        delete state.deletedPosts[postId];
      }
    },

    addCommentOptimistic: (state, action: PayloadAction<CommentPayload>) => {
      const { postId, text, parentCommentId, user, tempId } = action.payload;

      updatePostInAllCollections(state, postId, (post) => {
        post.totalCommentsCount = (post.totalCommentsCount || 0) + 1;
      });

      const comment = createOptimisticComment(user, text, tempId);

      if (!state.comments[postId]) {
        state.comments[postId] = { comments: [], total: 0, cursorId: null };
      }

      state.comments[postId].total = (state.comments[postId].total || 0) + 1;

      if (parentCommentId) {
        const parentComment = state.comments[postId].comments?.find(
          (c) => c.id === parentCommentId,
        );
        if (parentComment) {
          if (!parentComment.replies) parentComment.replies = [];
          parentComment.replies.push(comment);
          parentComment.repliesCount = (parentComment.repliesCount ?? 0) + 1;
        }
      } else {
        if (!state.comments[postId].comments) {
          state.comments[postId].comments = [];
        }
        state.comments[postId].comments.push(comment);
      }
    },

    updateCommentWithRealId: (
      state,
      action: PayloadAction<{
        postId: string;
        tempId: string;
        realId: string;
        parentCommentId?: string;
        cursorId?: number;
      }>,
    ) => {
      const { postId, tempId, realId, parentCommentId, cursorId } = action.payload;

      if (!state.comments[postId]) return;

      if (parentCommentId) {
        const parentComment = state.comments[postId].comments?.find(
          (c) => c.id === parentCommentId,
        );
        if (parentComment?.replies) {
          const reply = parentComment.replies.find((reply) => reply.id === tempId);
          if (reply) {
            reply.id = realId;
            if (cursorId !== undefined) reply.cursorId = cursorId;
          }
        }
      } else {
        const comment = state.comments[postId].comments?.find((c) => c.id === tempId);
        if (comment) {
          comment.id = realId;
          if (cursorId !== undefined) comment.cursorId = cursorId;
        }
      }
    },

    updateCommentRepliesCursor: (
      state,
      action: PayloadAction<{
        postId: string;
        parentCommentId: string;
        cursorId: number;
      }>,
    ) => {
      const { parentCommentId, cursorId } = action.payload;
      if (state.commentReplies[parentCommentId]) {
        state.commentReplies[parentCommentId].cursorId = cursorId;
      }
    },

    addScrapbookCommentOptimistic: (state, action: PayloadAction<ScrapbookCommentPayload>) => {
      const { scrapbookPostId, text, parentCommentId, user, tempId } = action.payload;

      updateScrapbookPostInAllCollections(state, scrapbookPostId, (post) => {
        post.commentCount! += 1;
      });

      const comment = createOptimisticScrapbookComment(user, text, tempId);

      if (!state.scrapbookComments[scrapbookPostId]) {
        state.scrapbookComments[scrapbookPostId] = { comments: [], total: 0, cursorId: null };
      }

      state.scrapbookComments[scrapbookPostId].total =
        (state.scrapbookComments[scrapbookPostId].total || 0) + 1;

      if (parentCommentId) {
        const parentComment = state.scrapbookComments[scrapbookPostId].comments?.find(
          (c) => c.id === parentCommentId,
        );
        if (parentComment) {
          if (!parentComment.replies) parentComment.replies = [];

          const existingReply = parentComment.replies.find((r) => r.id === tempId);
          if (!existingReply) {
            parentComment.replies.unshift(comment);
            parentComment.repliesCount = (parentComment.repliesCount || 0) + 1;
          }
        }
      } else {
        if (!state.scrapbookComments[scrapbookPostId].comments) {
          state.scrapbookComments[scrapbookPostId].comments = [];
        }

        const existingComment = state.scrapbookComments[scrapbookPostId].comments.find(
          (c) => c.id === tempId,
        );
        if (!existingComment) {
          state.scrapbookComments[scrapbookPostId].comments.unshift(comment);
        }
      }
    },

    updateScrapbookCommentWithRealId: (
      state,
      action: PayloadAction<{
        scrapbookPostId: string;
        tempId: string;
        realId: string;
        parentCommentId?: string;
        cursorId?: number;
      }>,
    ) => {
      const { scrapbookPostId, tempId, realId, parentCommentId, cursorId } = action.payload;

      if (!state.scrapbookComments[scrapbookPostId]) return;

      if (parentCommentId) {
        const parentComment = state.scrapbookComments[scrapbookPostId].comments?.find(
          (c) => c.id === parentCommentId,
        );
        if (parentComment?.replies) {
          const reply = parentComment.replies.find((reply) => reply.id === tempId);
          if (reply) {
            reply.id = realId;
            if (cursorId !== undefined) reply.cursorId = cursorId;
          }
        }
      } else {
        const comment = state.scrapbookComments[scrapbookPostId].comments?.find(
          (c) => c.id === tempId,
        );
        if (comment) {
          comment.id = realId;
          if (cursorId !== undefined) comment.cursorId = cursorId;
        }
      }
    },

    updateScrapbookCommentRepliesCursor: (
      state,
      action: PayloadAction<{
        scrapbookPostId: string;
        parentCommentId: string;
        cursorId: number;
      }>,
    ) => {
      const { parentCommentId, cursorId } = action.payload;
      if (state.scrapbookCommentReplies[parentCommentId]) {
        state.scrapbookCommentReplies[parentCommentId].cursorId = cursorId;
      }
    },

    removeCommentOptimistic: (state, action: PayloadAction<CommentActionPayload>) => {
      const { postId, commentId, parentCommentId } = action.payload;

      let commentsToRemove = 1;
      if (!parentCommentId) {
        const parentComment = state.comments[postId]?.comments?.find((c) => c.id === commentId);
        if (parentComment) {
          commentsToRemove = 1 + (parentComment.repliesCount || 0);
        }
      }

      updatePostInAllCollections(state, postId, (post) => {
        post.totalCommentsCount = Math.max(0, (post.totalCommentsCount || 0) - commentsToRemove);
      });

      if (!state.comments[postId]) return;

      state.comments[postId].total = Math.max(
        0,
        (state.comments[postId].total || 0) - commentsToRemove,
      );

      if (parentCommentId) {
        const parentComment = state.comments[postId].comments?.find(
          (c) => c.id === parentCommentId,
        );
        if (parentComment?.replies) {
          parentComment.replies = parentComment.replies.filter((reply) => reply.id !== commentId);
          parentComment.repliesCount = Math.max(0, (parentComment.repliesCount ?? 0) - 1);
        }

        if (state.commentReplies[parentCommentId]) {
          state.commentReplies[parentCommentId].comments = state.commentReplies[
            parentCommentId
          ].comments.filter((reply) => reply.id !== commentId);
          state.commentReplies[parentCommentId].total = Math.max(
            0,
            state.commentReplies[parentCommentId].total - 1,
          );
        }
      } else {
        if (state.comments[postId].comments) {
          state.comments[postId].comments = state.comments[postId].comments.filter(
            (comment) => comment.id !== commentId,
          );
        }
      }
    },

    removeScrapbookCommentOptimistic: (
      state,
      action: PayloadAction<ScrapbookCommentActionPayload>,
    ) => {
      const { scrapbookPostId, commentId, parentCommentId } = action.payload;

      let commentsToRemove = 1;
      if (!parentCommentId) {
        const parentComment = state.scrapbookComments[scrapbookPostId]?.comments?.find(
          (c) => c.id === commentId,
        );
        if (parentComment) {
          commentsToRemove = 1 + (parentComment.repliesCount || 0);
        }
      }

      updateScrapbookPostInAllCollections(state, scrapbookPostId, (post) => {
        post.commentCount! = Math.max(0, (post.commentCount || 0) - commentsToRemove);
      });

      if (!state.scrapbookComments[scrapbookPostId]) return;

      state.scrapbookComments[scrapbookPostId].total = Math.max(
        0,
        (state.scrapbookComments[scrapbookPostId].total || 0) - commentsToRemove,
      );

      if (parentCommentId) {
        const parentComment = state.scrapbookComments[scrapbookPostId].comments?.find(
          (c) => c.id === parentCommentId,
        );
        if (parentComment?.replies) {
          parentComment.replies = parentComment.replies.filter((reply) => reply.id !== commentId);
          parentComment.repliesCount = Math.max(0, (parentComment.repliesCount || 0) - 1);
        }

        if (state.scrapbookCommentReplies[parentCommentId]) {
          state.scrapbookCommentReplies[parentCommentId].comments = state.scrapbookCommentReplies[
            parentCommentId
          ].comments.filter((reply) => reply.id !== commentId);
          state.scrapbookCommentReplies[parentCommentId].total = Math.max(
            0,
            state.scrapbookCommentReplies[parentCommentId].total - 1,
          );
        }
      } else {
        if (state.scrapbookComments[scrapbookPostId].comments) {
          state.scrapbookComments[scrapbookPostId].comments = state.scrapbookComments[
            scrapbookPostId
          ].comments.filter((comment) => comment.id !== commentId);
        }
      }
    },

    updateCommentWithRealData: (
      state,
      action: PayloadAction<{
        postId: string;
        tempId: string;
        realId: string;
        cursorId: number;
        parentCommentId?: string;
      }>,
    ) => {
      const { postId, tempId, realId, cursorId, parentCommentId } = action.payload;

      if (!state.comments[postId]) return;

      if (parentCommentId) {
        const parentComment = state.comments[postId].comments?.find(
          (c) => c.id === parentCommentId,
        );
        if (parentComment?.replies) {
          const reply = parentComment.replies.find((reply) => reply.id === tempId);
          if (reply) {
            reply.id = realId;
            reply.cursorId = cursorId;
          }
        }
      } else {
        const comment = state.comments[postId].comments?.find((c) => c.id === tempId);
        if (comment) {
          comment.id = realId;
          comment.cursorId = cursorId;
        }
      }
    },

    updateScrapbookCommentWithRealData: (
      state,
      action: PayloadAction<{
        scrapbookPostId: string;
        tempId: string;
        realId: string;
        cursorId: number;
        parentCommentId?: string;
      }>,
    ) => {
      const { scrapbookPostId, tempId, realId, cursorId, parentCommentId } = action.payload;

      if (!state.scrapbookComments[scrapbookPostId]) return;

      if (parentCommentId) {
        const parentComment = state.scrapbookComments[scrapbookPostId].comments?.find(
          (c) => c.id === parentCommentId,
        );
        if (parentComment?.replies) {
          const reply = parentComment.replies.find((reply) => reply.id === tempId);
          if (reply) {
            reply.id = realId;
            reply.cursorId = cursorId;
          }
        }
      } else {
        const comment = state.scrapbookComments[scrapbookPostId].comments?.find(
          (c) => c.id === tempId,
        );
        if (comment) {
          comment.id = realId;
          comment.cursorId = cursorId;
        }
      }
    },
  },
  extraReducers: (builder) => {
    builder
      .addCase(fetchPosts.fulfilled, (state, action) => {
        const { response, refresh } = action.payload;

        const mergeWithExistingLikeStates = (newPosts: PostExternalClientI[]) => {
          return newPosts.map((newPost) => {
            const existingInGlobal = state.posts.find((p) => p.id === newPost.id);
            const existingInProfile = Object.values(state.profilePosts)
              .flatMap((profile) => profile.posts)
              .find((p) => p.id === newPost.id);

            const existingPost = existingInGlobal || existingInProfile;
            if (existingPost) {
              return {
                ...newPost,
                isLiked: existingPost.isLiked,
                reactionsCount: existingPost.reactionsCount,
              };
            }
            return newPost;
          });
        };

        if (refresh) {
          state.posts = mergeWithExistingLikeStates(response.posts);
        } else {
          const existingIds = new Set(state.posts.map((post) => post.id));
          const newPosts = response.posts.filter((post) => !existingIds.has(post.id));
          state.posts.push(...mergeWithExistingLikeStates(newPosts));
        }

        state.pagination = {
          cursorId: response.cursorId?.toString() ?? null,
          otherCursorId: response.otherCursorId,
          hasMore: response.posts.length > 0,
        };
      })
      .addCase(fetchPost.fulfilled, (state, action) => {
        const { post } = action.payload;
        state.post = post;
      })
      .addCase(fetchPost.rejected, (state) => {
        state.post = null;
      })
      .addCase(fetchProfilePosts.fulfilled, (state, action) => {
        const { profileId, response, refresh, globalState } = action.payload;
        const profileIdStr = profileId as string;
        const currentUser = selectCurrentUser(globalState);
        const isOwnProfile = profileIdStr === currentUser.profileId;

        const mergeWithExistingLikeStates = (newPosts: PostExternalClientI[]) => {
          return newPosts.map((newPost) => {
            const existingInGlobal = state.posts.find((p) => p.id === newPost.id);
            const existingInProfile = isOwnProfile
              ? state.ownPosts.find((p) => p.id === newPost.id)
              : state.profilePosts[profileIdStr]?.posts.find((p) => p.id === newPost.id);

            const existingPost = existingInGlobal || existingInProfile;
            if (existingPost) {
              return {
                ...newPost,
                isLiked: existingPost.isLiked,
                reactionsCount: existingPost.reactionsCount,
              };
            }
            return newPost;
          });
        };

        const posts = refresh
          ? mergeWithExistingLikeStates(response.posts)
          : [
              ...(isOwnProfile ? state.ownPosts : state.profilePosts[profileIdStr]?.posts || []),
              ...mergeWithExistingLikeStates(
                response.posts.filter(
                  (post) =>
                    !(
                      isOwnProfile ? state.ownPosts : state.profilePosts[profileIdStr]?.posts || []
                    ).some((existingPost) => existingPost.id === post.id),
                ),
              ),
            ];

        const pagination = {
          cursorId: response.cursorId?.toString() ?? null,
          hasMore: !!response.cursorId,
        };

        if (isOwnProfile) {
          state.ownPosts = posts;
          state.ownPostsPagination = pagination;
        } else {
          if (!state.profilePosts[profileIdStr]) {
            state.profilePosts[profileIdStr] = {
              posts: [],
              pagination: { cursorId: null, hasMore: true },
            };
          }
          state.profilePosts[profileIdStr].posts = posts;
          state.profilePosts[profileIdStr].pagination = pagination;
        }
      })
      .addCase(fetchScrapbookPosts.fulfilled, (state, action) => {
        const { response, refresh, page, pageSize } = action.payload;

        if (refresh) {
          state.scrapbookPosts = response.data;
        } else {
          const existingIds = new Set(state.scrapbookPosts.map((post) => post.id));
          const newPosts = response.data.filter((post) => !existingIds.has(post.id));
          state.scrapbookPosts.push(...newPosts);
        }

        state.scrapbookPagination = {
          page,
          hasMore:
            state.scrapbookPosts.length < response.total && response.data.length === pageSize,
          total: response.total,
        };
      })
      .addCase(fetchScrapbookCommentsForPost.fulfilled, (state, action) => {
        const { scrapbookPostId, comments, isInitialFetch } = action.payload;

        if (isInitialFetch || !state.scrapbookComments[scrapbookPostId]) {
          state.scrapbookComments[scrapbookPostId] = comments;
        } else {
          const existingIds = new Set(
            state.scrapbookComments[scrapbookPostId].comments?.map((comment) => comment.id) || [],
          );
          const newComments =
            comments.comments?.filter((comment) => !existingIds.has(comment.id)) || [];

          if (newComments.length > 0) {
            state.scrapbookComments[scrapbookPostId] = {
              ...comments,
              comments: [
                ...(state.scrapbookComments[scrapbookPostId].comments || []),
                ...newComments,
              ],
            };
          }
        }
      })
      .addCase(fetchScrapbookCommentReplies.fulfilled, (state, action) => {
        const { parentCommentId, replies, isInitialFetch } = action.payload;

        if (isInitialFetch || !state.scrapbookCommentReplies[parentCommentId]) {
          state.scrapbookCommentReplies[parentCommentId] = {
            comments: replies.comments || [],
            cursorId: replies.cursorId ?? null,
            total: replies.total ?? 0,
          };
        } else {
          const existingIds = new Set(
            state.scrapbookCommentReplies[parentCommentId].comments?.map((reply) => reply.id) || [],
          );
          const newReplies = replies.comments?.filter((reply) => !existingIds.has(reply.id)) || [];

          if (newReplies.length > 0) {
            state.scrapbookCommentReplies[parentCommentId] = {
              comments: [
                ...(state.scrapbookCommentReplies[parentCommentId].comments || []),
                ...newReplies,
              ],
              cursorId: replies.cursorId ?? null,
              total: replies.total ?? 0,
            };
          }
        }
      })
      .addCase(fetchScrapbookReactionsForPost.fulfilled, (state, action) => {
        const { postId, reactions, totalCount } = action.payload;
        state.scrapbookReactions[postId] = { reactions, totalCount };
      })
      .addCase(createPost.fulfilled, (state, action) => {
        const { response, tempId } = action.payload;
        if (response && tempId) {
          const updatedPost: PostExternalClientI = {
            id: response.id,
            caption: response.caption || '',
            reactionsCount: 0,
            totalCommentsCount: 0,
            createdAt: response.createdAt
              ? typeof response.createdAt === 'string'
                ? response.createdAt
                : response.createdAt?.toISOString?.() || new Date().toISOString()
              : new Date().toISOString(),
            Media: response.Media || [],
            isLiked: false,
            Profile: state.posts.find((p) => p.id === tempId)?.Profile || {
              id: '',
              name: 'Unknown User',
              avatar: null,
              entity: null,
              designation: null,
            },
          };

          updatePostInAllCollections(state, tempId, (post) => {
            Object.assign(post, {
              id: response.id,
              createdAt: updatedPost.createdAt,
              caption: updatedPost.caption,
              Media: updatedPost.Media,
            });
          });
        }
      })
      .addCase(editPost.fulfilled, (state, action) => {
        const { response, postId } = action.payload;
        updatePostInAllCollections(state, postId, (post) => {
          post.caption = response.caption || '';
          post.Media = response.Media || [];
        });
      })
      .addCase(createScrapbookPost.fulfilled, (state, action) => {
        const { response, tempId } = action.payload;

        if (response && tempId) {
          updateScrapbookPostInAllCollections(state, tempId, (post) => {
            Object.assign(post, {
              id: response.id,
              createdAt: response.createdAt || post.createdAt,
              textPreview: response.textPreview || post.textPreview,
              reactionCount: response.reactionCount || post.reactionCount,
              commentCount: response.commentCount || post.commentCount,
            });
          });
        }
      })
      .addCase(editScrapbookPost.fulfilled, (state, action) => {
        const { postId, payload } = action.payload;
        updateScrapbookPostInAllCollections(state, postId, (post) => {
          if ('textPreview' in post) {
            post.textPreview = payload.text.trim().slice(0, 120);
          } else {
            post.caption = payload.text.trim().slice(0, 120);
          }
        });
      })
      .addCase(fetchReactionsForPost.fulfilled, (state, action) => {
        const { postId, reactions, totalCount } = action.payload;
        state.reactions[postId] = { reactions, totalCount };
      })
      .addCase(fetchCommentsForPost.fulfilled, (state, action) => {
        const { postId, comments, isInitialFetch } = action.payload;

        if (isInitialFetch || !state.comments[postId]) {
          state.comments[postId] = comments;
        } else {
          const existingIds = new Set(
            state.comments[postId].comments?.map((comment) => comment.id) || [],
          );
          const newComments =
            comments.comments?.filter((comment) => !existingIds.has(comment.id)) || [];

          if (newComments.length > 0) {
            state.comments[postId] = {
              ...comments,
              comments: [...(state.comments[postId].comments || []), ...newComments],
            };
          }
        }
      })
      .addCase(fetchCommentReplies.fulfilled, (state, action) => {
        const { parentCommentId, replies, isInitialFetch } = action.payload;

        if (isInitialFetch || !state.commentReplies[parentCommentId]) {
          state.commentReplies[parentCommentId] = {
            comments: replies.comments || [],
            cursorId: replies.cursorId ?? null,
            total: replies.total ?? 0,
          };
        } else {
          const existingIds = new Set(
            state.commentReplies[parentCommentId].comments?.map((reply) => reply.id) || [],
          );
          const newReplies = replies.comments?.filter((reply) => !existingIds.has(reply.id)) || [];

          if (newReplies.length > 0) {
            state.commentReplies[parentCommentId] = {
              comments: [...(state.commentReplies[parentCommentId].comments || []), ...newReplies],
              cursorId: replies.cursorId ?? null,
              total: replies.total ?? 0,
            };
          }
        }
      });
  },
});

export const {
  resetContentState,
  resetScrapbookState,
  resetSearchPosts,
  setSearchPosts,
  addPostOptimistic,
  removePostOptimistic,
  updatePostWithRealId,
  addScrapbookPostOptimistic,
  removeScrapbookPostOptimistic,
  updateScrapbookPostWithRealId,
  addReactionOptimistc,
  removeReactionOptimistic,
  addScrapbookReactionOptimistic,
  removeScrapbookReactionOptimistic,
  addCommentOptimistic,
  updateCommentWithRealId,
  updateCommentWithRealData,
  updateCommentRepliesCursor,
  removeCommentOptimistic,
  addScrapbookCommentOptimistic,
  updateScrapbookCommentWithRealId,
  updateScrapbookCommentWithRealData,
  updateScrapbookCommentRepliesCursor,
  removeScrapbookCommentOptimistic,
  deletePostOptimistic,
  deleteScrapbookPostOptimistic,
  revertDeleteScrapbookPostOptimistic,
  revertDeletePostOptimistic,
} = contentSlice.actions;

export default contentSlice.reducer;
