import { useState } from 'react';
import React from 'react';
import {
  Text,
  View,
  ActivityIndicator,
  RefreshControl,
  Share as RNShare,
  Pressable,
} from 'react-native';
import { useNavigation } from '@react-navigation/native';
import { FlatList } from 'react-native-gesture-handler';
import { useDispatch, useSelector } from 'react-redux';
import BackButton from '@/src/components/BackButton';
import BottomSheet from '@/src/components/Bottomsheet';
import CustomModal from '@/src/components/Modal';
import NotFound from '@/src/components/NotFound';
import { OptionItem, OptionsMenu } from '@/src/components/OptionsMenu';
import Tabs from '@/src/components/Tabs';
import UserPost from '@/src/components/UserPost';
import { selectCurrentUser } from '@/src/redux/selectors/user';
import { AppDispatch } from '@/src/redux/store';
import { handleError } from '@/src/utilities/errors/errors';
import { showToast } from '@/src/utilities/toast';
import { AppStackParamListI, BottomTabNavigationI } from '@/src/navigation/types';
import AddConnection from '@/src/assets/svgs/AddConnection';
import Block from '@/src/assets/svgs/Block';
import HorizontalEllipsis from '@/src/assets/svgs/HorizontalEllipsis';
import Settings from '@/src/assets/svgs/Settings';
import Share from '@/src/assets/svgs/Share';
import { blockProfileAPI } from '@/src/networks/connect/blocking';
import { PostExternalClientI } from '@/src/networks/content/types';
import AboutScreen from '../About';
import ExperienceScreen from '../Experience';
import ProfileHeader from '../ProfileHeader';
import { ProfileContentProps } from './types';
import useProfileContent from './useHook';

const INITIAL_NUM_TO_RENDER = 5;
const END_REACHED_THRESHOLD = 0.8;
const WINDOW_SIZE = 5;

const ListFooter = ({ isLoading }: { isLoading: boolean }) => {
  if (!isLoading) return null;
  return (
    <View className="py-4">
      <ActivityIndicator size="small" />
    </View>
  );
};

const ProfileContent = ({ data, isUserProfile, aboutProfile }: ProfileContentProps) => {
  const navigation = useNavigation<BottomTabNavigationI>();
  const dispatch = useDispatch<AppDispatch>();
  const currentUser = useSelector(selectCurrentUser);

  const [activeTab, setActiveTab] = useState('about');
  const [isBottomSheetOpen, setIsBottomSheetOpen] = useState(false);
  const [isBlockModalVisible, setIsBlockModalVisible] = useState(false);
  const [isBlocking, setIsBlocking] = useState(false);

  const {
    profilePosts,
    refreshing,
    loading,
    following,
    setFollowing,
    handleRefresh,
    handleLoadMore,
    handleLikePost,
    handleDeletePost,
    handleFollow,
  } = useProfileContent(data, data?.profileId, aboutProfile);

  const tabs = [
    { id: 'about', label: 'About' },
    { id: 'experience', label: 'Experience' },
    { id: 'posts', label: 'Posts' },
  ];

  const handleBack = () => navigation.goBack();
  const handleOpenOptions = () => setIsBottomSheetOpen(true);
  const handleCloseOptions = () => setIsBottomSheetOpen(false);

  const handleFollowRequest = () => {
    setFollowing(!following);
    setIsBottomSheetOpen(false);
    handleFollow().catch(() => {
      setFollowing(following);
    });
  };

  const handleOpenBlockModal = () => {
    setIsBottomSheetOpen(false);
    setTimeout(() => {
      setIsBlockModalVisible(true);
    }, 500);
  };

  const handleCancelBlock = () => {
    setIsBlockModalVisible(false);
  };

  const handleConfirmBlock = async () => {
    try {
      setIsBlocking(true);
      await blockProfileAPI({ toBlockId: data.profileId });
      setIsBlockModalVisible(false);
      showToast({
        type: 'success',
        message: 'User Blocked',
        description: 'You will no longer see content from this user',
      });
      navigation.navigate('HomeStack', { screen: 'Home' });
    } catch (error) {
      showToast({
        type: 'error',
        message: 'Error',
        description: 'Failed to block user',
      });
    } finally {
      setIsBlocking(false);
    }
  };

  const handleSettings = () => {
    setIsBottomSheetOpen(false);
    navigation.navigate('ProfileStack', { screen: 'UserSettings' });
  };

  const handleShareProfile = async () => {
    try {
      const profileUrl = `https://network.navicater.com/profile/${data.profileId}`;
      const result = await RNShare.share({
        message: `Check out ${data.name}'s profile on Navicater: ${profileUrl}`,
        url: profileUrl,
        title: `${data.name}'s Profile`,
      });

      if (result.action === RNShare.sharedAction) {
        showToast({
          type: 'success',
          message: 'Shared Successfully!',
          description: 'Your profile has been shared 🎉',
        });
      }
      setIsBottomSheetOpen(false);
    } catch (error) {
      handleError(error);
    }
  };

  const renderTabContent = () => {
    const state = navigation.getState();
    const activeRouteName = state.routes[state.index].name as keyof AppStackParamListI;

    const handleEndReached = () => {
      if (!loading && !refreshing) {
        handleLoadMore();
      }
    };

    const navigateToLikesScreen = (params: { postId: string }) => {
      const screen = 'Likes';
      const stack = activeRouteName === 'UserProfile' ? 'ProfileStack' : 'HomeStack';
      navigation.navigate(stack, { screen, params: { ...params, type: 'USER_POST' } });
    };

    const navigateToCommentsScreen = (params: { postId: string }) => {
      const screen = 'Comment';
      const stack = activeRouteName === 'UserProfile' ? 'ProfileStack' : 'HomeStack';
      navigation.navigate(stack, { screen, params });
    };

    const renderItem = ({ item }: { item: PostExternalClientI }) => (
      <UserPost
        post={item}
        onLikePress={() => handleLikePost(item, 'LIKE')}
        onDeletePress={() => handleDeletePost(item)}
        isOwnPost={item.Profile.id === currentUser.profileId}
        onEditPress={() => {
          navigation.navigate('CreateStack', {
            screen: 'CreateContent',
            params: { postId: item.id, editing: true, type: 'USER_POST' },
          });
        }}
        onLikeCountPress={() => navigateToLikesScreen({ postId: item.id })}
        onCommentPress={() => navigateToCommentsScreen({ postId: item.id })}
      />
    );

    if (activeTab === 'posts') {
      return (
        <View className="flex-1">
          {!profilePosts?.length ? (
            <NotFound title="Why so quiet?" subtitle="Create some amazing posts and network" />
          ) : (
            <FlatList
              data={profilePosts}
              renderItem={renderItem}
              keyExtractor={(item) => item.id}
              showsVerticalScrollIndicator={false}
              className="flex-1"
              contentContainerClassName="pb-4"
              initialNumToRender={INITIAL_NUM_TO_RENDER}
              maxToRenderPerBatch={INITIAL_NUM_TO_RENDER}
              windowSize={WINDOW_SIZE}
              removeClippedSubviews
              refreshControl={
                <RefreshControl enabled={true} refreshing={refreshing} onRefresh={handleRefresh} />
              }
              onEndReached={handleEndReached}
              onEndReachedThreshold={END_REACHED_THRESHOLD}
              ListFooterComponent={<ListFooter isLoading={loading} />}
            />
          )}
        </View>
      );
    }

    if (activeTab === 'about') {
      return <AboutScreen isUserProfile={isUserProfile} />;
    }

    if (activeTab === 'experience') {
      return (
        <View className="p-4 flex-1">
          <ExperienceScreen isUserProfile={isUserProfile} profileId={data?.profileId} />
        </View>
      );
    }

    return null;
  };

  if (!data) return null;

  return (
    <>
      <BottomSheet
        visible={isBottomSheetOpen}
        onModalHide={() => {}}
        onClose={handleCloseOptions}
        height={isUserProfile ? 150 : 200}
      >
        <OptionsMenu>
          <OptionItem icon={<Share />} label="Share Profile" onPress={handleShareProfile} />
          {!isUserProfile && (
            <OptionItem
              icon={<AddConnection width={2.5} height={2.5} />}
              label={following ? 'Unfollow' : 'Follow'}
              onPress={handleFollowRequest}
            />
          )}
          {!isUserProfile && (
            <OptionItem icon={<Block />} label="Block User" onPress={handleOpenBlockModal} />
          )}
          {isUserProfile && (
            <OptionItem
              icon={<Settings width={2} height={2} />}
              label="Settings"
              onPress={handleSettings}
            />
          )}
        </OptionsMenu>
      </BottomSheet>

      {isBlockModalVisible && (
        <CustomModal
          isVisible={isBlockModalVisible}
          title={`Block ${data.name}?`}
          description="Blocking this user will prevent them from seeing your posts, and you won't see their posts anymore."
          onCancel={handleCancelBlock}
          onConfirm={handleConfirmBlock}
          isConfirming={isBlocking}
          confirmText={isBlocking ? 'Blocking' : 'Block'}
          confirmButtonVariant="danger"
          cancelText="Cancel"
        />
      )}

      <View className="flex-1">
        <View className="justify-between flex-row items-center px-4">
          <BackButton onBack={handleBack} label="" />
          <Text className="font-medium">{`@${data.username}`}</Text>
          <Pressable onPress={handleOpenOptions}>
            <HorizontalEllipsis />
          </Pressable>
        </View>
        <ProfileHeader
          key={aboutProfile?.profileId || aboutProfile?.request?.status}
          data={data}
          isUserProfile={isUserProfile}
          aboutProfile={aboutProfile}
          setFollowing={setFollowing}
        />
        <View className="mt-6">
          <Tabs tabs={tabs} activeTab={activeTab} onTabChange={setActiveTab} />
        </View>
        {renderTabContent()}
      </View>
    </>
  );
};

export default ProfileContent;
