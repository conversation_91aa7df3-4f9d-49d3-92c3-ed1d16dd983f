import { SearchResultI } from '@/src/redux/slices/entitysearch/types';
import {
  FieldTypeI,
  ShipCreateEditPayloadI,
  ShipDetailsFormDataI,
  ShipPayloadI,
  ShipPreFilledDataTypeI,
} from './EditShipItem/types';

export const generateShipAddEditPayload = (
  preFilledData: ShipPreFilledDataTypeI[],
  data: ShipDetailsFormDataI,
  shipId: string | undefined,
  field: FieldTypeI,
  localSkills: SearchResultI[],
  initialSkills: SearchResultI[],
) => {
  const deletedSkills = initialSkills
    ?.filter((initial) => !localSkills.some((local) => local.id === initial.id))
    .map(({ id, dataType }) => ({ id, dataType }));

  const addedskills = localSkills
    ?.filter((local) => !initialSkills.some((initial) => initial.id === local.id))
    .map(({ id, dataType }) => ({ id, dataType }));

  let ship: ShipPayloadI = {
    opr: shipId ? 'UPDATE' : 'CREATE',
    ship: data.imoNumber,
    name: data.shipName,
    sizeGt: parseFloat(data.grossTonnage),
    powerKw: parseFloat(data.power),
    fromDate: data.fromDate,
    toDate: data.toDate,
    department: {
      id: data.department.id,
      dataType: data.department.dataType,
    },
    subVesselType: {
      id: data.shipType.id,
      dataType: data.shipType.dataType,
    },
    dwt: parseFloat(data.deadWeight),
  };

  if (shipId) {
    ship['id'] = shipId;
  }
  if (data?.additionalDetails) {
    ship['details'] = data?.additionalDetails;
  }

  if (localSkills?.length > 0 || addedskills?.length > 0 || deletedSkills?.length > 0) {
    ship['skills'] = shipId
      ? [
          ...addedskills?.map((item) => {
            return {
              opr: 'CREATE',
              id: item.id,
              dataType: item.dataType,
            };
          }),
          ...deletedSkills?.map((item) => {
            return {
              id: item.id,
              opr: 'DELETE',
              dataType: item.dataType,
            };
          }),
        ]
      : localSkills?.map((item) => {
          return {
            opr: 'CREATE',
            id: item.id,
            dataType: item.dataType,
          };
        });
  }

  let payload = JSON.parse(JSON.stringify(preFilledData));

  if (field.id) {
    for (const exp of payload) {
      for (const desig of exp.designations) {
        if (desig.id === field.id) {
          if (!Array.isArray(desig.ships)) {
            desig.ships = [];
          }
          desig.ships.push(ship);
        }
      }
    }
  } else {
    for (const exp of payload) {
      for (const desig of exp.designations) {
        if (
          desig.designation.id === field.designation.id &&
          desig.fromDate === field.fromDate &&
          desig.toDate === field.toDate
        ) {
          if (!Array.isArray(desig.ships)) {
            desig.ships = [];
          }
          desig.ships.push(ship);
        }
      }
    }
  }

  return payload as ShipCreateEditPayloadI[];
};

export const formatDateToYMD = (date: string | Date | null | undefined): string => {
  if (!date) return '';
  const d = new Date(date);
  if (isNaN(d.getTime())) return '';

  const year = d.getFullYear();
  const month = String(d.getMonth() + 1).padStart(2, '0');
  const day = String(d.getDate()).padStart(2, '0');
  return `${year}-${month}-${day}`;
};

export const validateFromDate = (
  fromDate: string,
  toDate: string | null,
  fieldFromDate: string,
  fieldToDate: string,
) => {
  if (new Date(fromDate) < new Date(fieldFromDate)) {
    return `Date cannot be before ${toMonthYear(fieldFromDate)}`;
  }

  if (toDate && new Date(fromDate) > new Date(toDate)) {
    return 'From date cannot be after To date';
  }

  if (new Date(fromDate) > new Date(fieldToDate)) {
    return `Date cannot be after ${toMonthYear(fieldToDate)}`;
  }

  return true;
};

export const toMonthYear = (dateString: string | null) => {
  if (!dateString) return 'Present';

  const date = new Date(dateString);
  return date.toLocaleString('default', {
    month: 'long',
    year: 'numeric',
  });
};

export const validateToDate = (
  toDate: string | null,
  isPresent: boolean,
  fromDate: string,
  fieldFromDate: string,
  fieldToDate: string,
) => {
  if (!isPresent && !toDate) return 'To Date cannot be null';

  if (!toDate) return true;

  if (new Date(toDate) < new Date(fromDate)) {
    return 'To date cannot be before From date';
  }

  if (new Date(toDate) > new Date(fieldToDate)) {
    return `Date cannot be after ${toMonthYear(fieldToDate)}`;
  }

  if (new Date(toDate) < new Date(fieldFromDate)) {
    return `Date cannot be before ${toMonthYear(fieldFromDate)}`;
  }

  return true;
};
