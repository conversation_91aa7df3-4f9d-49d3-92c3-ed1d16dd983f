import { SearchResultI } from '@/src/redux/slices/entitysearch/types';
import { MessageContent } from '@/src/networks/chat/types';

export interface ChatItemProps {
  item: ChatListItem;
}

export interface ChatListItem {
  id: string;
  senderId: string;
  recieverId: string;
  content: MessageContent;
  readAt: Date | null;
  createdAt: Date;
  profile?: ProfileData;
}

export interface ProfileData {
  email: string;
  name: string;
  username: string;
  avatar: string | null;
  profileId: string;
  designation: SearchResultI | null;
  entity: SearchResultI | null;
}
