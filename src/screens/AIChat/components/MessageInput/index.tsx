import type React from 'react';
import { useState, useRef } from 'react';
import { View, TextInput, Pressable, Platform, ActivityIndicator } from 'react-native';
import Send from '@/src/assets/svgs/Send';
import type { MessageInputProps } from './types';
import { useMessageInput } from './useMessageInput';

export const MessageInput: React.FC<MessageInputProps> = ({
  value,
  onChangeText,
  onSend,
  loading,
}) => {
  const { handleKeyPress } = useMessageInput(onSend);
  const [inputHeight, setInputHeight] = useState(40);
  const textInputRef = useRef<TextInput>(null);

  const handleContentSizeChange = (event: any) => {
    const newHeight = Math.min(Math.max(40, event.nativeEvent.contentSize.height), 120);
    setInputHeight(newHeight);
  };

  return (
    <View className="bg-white border-t border-gray-100 shadow-sm px-4 py-4">
      <View className="flex-row items-end gap-3">
        <View
          className="flex-1 bg-gray-100 rounded-3xl px-5 shadow-sm"
          style={{ minHeight: Math.max(48, inputHeight + 8) }}
        >
          <TextInput
            ref={textInputRef}
            value={value}
            onChangeText={onChangeText}
            onContentSizeChange={handleContentSizeChange}
            placeholder="Type a message..."
            placeholderTextColor="#9CA3AF"
            className="text-base text-gray-900 leading-6 py-3"
            multiline
            textAlignVertical="top"
            style={{
              maxHeight: 120,
              minHeight: 24,
            }}
            editable={!loading}
            scrollEnabled={inputHeight >= 120}
            onKeyPress={handleKeyPress}
            returnKeyType="send"
            blurOnSubmit={Platform.OS === 'android'}
          />
        </View>

        <Pressable
          className={`w-12 h-12 rounded-full items-center justify-center shadow-md ${
            value.trim() ? 'bg-[#448600]' : 'bg-gray-400'
          } ${loading ? 'opacity-60' : ''}`}
          onPress={onSend}
          disabled={loading || !value.trim()}
        >
          {loading ? (
            <ActivityIndicator size="small" color="white" />
          ) : (
            <Send color="white" width={2} height={2} />
          )}
        </Pressable>
      </View>
    </View>
  );
};
