import type React from 'react';
import { View, Text } from 'react-native';
import TypingDots from '../TypingDots';
import type { MessageBubbleProps } from './types';
import { useMessageBubble } from './useMessageBubble';

export const MessageBubble: React.FC<MessageBubbleProps> = ({
  message,
  isLoading,
  streamedText,
  currentAiMessageId,
}) => {
  const { formatMessageTime } = useMessageBubble();
  const isAI = message.from === 'ai';
  const isLastAI = isAI && isLoading && !message.text;
  const isCurrentStreaming = currentAiMessageId === message.id && streamedText;

  const displayText = isCurrentStreaming ? streamedText : message.text;

  return (
    <View className={`mb-2 ${isAI ? 'self-start' : 'self-end'} max-w-[80%]`}>
      <View
        className={`px-4 py-3 rounded-2xl ${
          isAI
            ? 'bg-white rounded-bl-md shadow-sm border border-gray-100'
            : 'bg-[#7ba155] rounded-br-md shadow-sm'
        }`}
      >
        {isLastAI ? (
          <View className="flex-row items-center">
            <TypingDots />
          </View>
        ) : (
          <Text className={`text-base leading-6 ${isAI ? 'text-gray-900' : 'text-white'}`}>
            {displayText}
          </Text>
        )}
        <View className="flex-row items-center justify-end mt-2">
          <Text className={`text-xs ${isAI ? 'text-gray-500' : 'text-white/70'}`}>
            {formatMessageTime(new Date(message.timestamp))}
          </Text>
        </View>
      </View>
    </View>
  );
};
