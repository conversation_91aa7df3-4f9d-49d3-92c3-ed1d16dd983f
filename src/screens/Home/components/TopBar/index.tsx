/*
Copyright (c) 2025-present Navicater Solutions

This source code is licensed under the license found in the
LICENSE file in the root directory of this source tree.
*/
import React from 'react';
import { Pressable, Text, View } from 'react-native';
import Location from '@/src/assets/svgs/Location';
import Search from '@/src/assets/svgs/Search';
import TopBarChat from '@/src/assets/svgs/TopBarChat';
import { TopBarProps } from './types';

const TopBar: React.FC<TopBarProps> = React.memo(({ onSearchPress, onMessagePress }) => (
  <View className="px-4 py-2 flex-row items-center gap-3">
    <Pressable
      className="flex-1 flex-row items-center px-4 py-3 bg-gray-100 rounded-xl"
      onPress={onSearchPress}
    >
      <Search />
      <Text className="ml-2 text-base text-gray-500">Search</Text>
    </Pressable>
    <View className="flex flex-row gap-1">
      <Pressable className="w-12 h-12 items-center justify-center" onPress={onMessagePress}>
        <Location />
      </Pressable>
      <Pressable className="w-12 h-12 items-center justify-center" onPress={onMessagePress}>
        <TopBarChat />
      </Pressable>
    </View>
  </View>
));

export default TopBar;
