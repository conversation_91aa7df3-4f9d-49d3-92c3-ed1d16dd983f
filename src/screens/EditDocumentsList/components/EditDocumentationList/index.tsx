/*
Copyright (c) 2025-present Navicater Solutions

This source code is licensed under the license found in the
LICENSE file in the root directory of this source tree.
*/
import { SetStateAction, useState } from 'react';
import { Pressable, Text, View } from 'react-native';
import BackButton from '@/src/components/BackButton';
import Tabs from '@/src/components/Tabs';
import AddItem from '@/src/assets/svgs/AddItem';
import { EditIdentityDocuments } from '../EditIdentityDocuments';
import { EditVisaDocuments } from '../EditVisaDocuments';
import { EditDocumentsListPropsI, EditDocumentTabsI } from './types';

const EditDocumentsList = ({
  onBack,
  profileId,
  onAdd,
  editable,
  tab,
}: EditDocumentsListPropsI) => {
  const tabs = [
    {
      id: 'identity',
      label: 'Identity',
    },
    {
      id: 'visa',
      label: 'Visa',
    },
  ];

  const tabScreens: EditDocumentTabsI = {
    identity: <EditIdentityDocuments profileId={profileId} editable={editable} />,
    visa: <EditVisaDocuments profileId={profileId} editable={editable} />,
  };

  const [activeTab, setActiveTab] = useState<keyof EditDocumentTabsI>(tab);

  return (
    <View className="flex-1 px-4">
      <View className="flex-row items-center justify-between py-4">
        <View className="flex-row items-center gap-1">
          <BackButton onBack={onBack} label="" />
          <Text className="text-xl font-medium">Edit Documents</Text>
        </View>
        <View className="mr-5">
          <Pressable onPress={onAdd}>
            <AddItem />
          </Pressable>
        </View>
      </View>

      <>
        <Tabs
          tabs={tabs}
          activeTab={activeTab}
          onTabChange={setActiveTab as React.Dispatch<SetStateAction<string>>}
        />
        {tabScreens[activeTab]}
      </>
    </View>
  );
};

export default EditDocumentsList;
