import { Pressable, Text, View } from 'react-native';
import Close from '@/src/assets/svgs/Close';
import { MessageI } from './types';

const ReplyPreview = ({ message, onClose }: { message: MessageI; onClose?: () => void }) => (
  <View className="border border-gray-200 rounded-md p-3 flex-row items-start gap-2 mx-3 mt-1">
    <View className="w-1 bg-primaryGreen self-stretch rounded-lg" />
    <View className="flex-1 flex-row justify-between">
      <View className="flex-1">
        <Text className="text-xs font-medium text-gray-600">{message.user?.name || 'Unknown'}</Text>
        <Text className="text-sm text-gray-800" numberOfLines={2}>
          {message.deletedForAll
            ? 'This message was deleted'
            : message.content.text || 'Media message'}
        </Text>
      </View>
      {onClose && (
        <Pressable onPress={onClose} className="p-1">
          <Close width={2} height={2} color="#6B7280" />
        </Pressable>
      )}
    </View>
  </View>
);

export default ReplyPreview;
