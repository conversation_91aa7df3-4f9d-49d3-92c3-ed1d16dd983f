import { View } from 'react-native';
import { RFPercentage } from 'react-native-responsive-fontsize';
import BottomSheet from '@/src/components/Bottomsheet/index';
import { OptionItem, OptionsMenu } from '@/src/components/OptionsMenu';
import Edit from '@/src/assets/svgs/EditPencil';
import Send from '@/src/assets/svgs/Send';
import TrashBin from '@/src/assets/svgs/TrashBin';
import type { MoreOptionsPropsI } from './types';

const MoreOptions = (props: MoreOptionsPropsI) => {
  const {
    onClose,
    onDeleteForMe,
    onDeleteForEveryone,
    onReply,
    onEdit,
    optionsVisible,
    canEdit,
    canDeleteForEveryone,
  } = props;

  const getOptionsCount = () => {
    let count = 2;
    if (canEdit) count++;
    if (canDeleteForEveryone) count++;
    return count;
  };

  const getBottomSheetHeight = () => {
    const optionsCount = getOptionsCount();
    const heightPerOption = 8;
    const separatorHeight = 1;
    const totalSeparators = optionsCount;

    return RFPercentage(optionsCount * heightPerOption + totalSeparators * separatorHeight);
  };

  return (
    <BottomSheet
      onModalHide={() => {}}
      height={getBottomSheetHeight()}
      visible={optionsVisible}
      onClose={onClose}
    >
      <OptionsMenu>
        <OptionItem
          icon={<Send color="#000000" width={2} height={2} />}
          label="Reply"
          onPress={onReply}
        />
        <View className="h-[1px] bg-gray-200 my-2" />
        {canEdit && (
          <>
            <OptionItem icon={<Edit width={2} height={2} />} label="Edit" onPress={onEdit} />
            <View className="h-[1px] bg-gray-200 my-2" />
          </>
        )}
        <OptionItem
          icon={<TrashBin width={2} height={2} />}
          label="Delete for me"
          textClassName="text-orange-500"
          onPress={onDeleteForMe}
        />
        {canDeleteForEveryone && (
          <>
            <View className="h-[1px] bg-gray-200 my-2" />
            <OptionItem
              icon={<TrashBin width={2} height={2} />}
              label="Delete for everyone"
              textClassName="text-red-500"
              onPress={onDeleteForEveryone}
            />
          </>
        )}
      </OptionsMenu>
    </BottomSheet>
  );
};

export default MoreOptions;
