'use client';

import { useState, useRef } from 'react';
import {
  View,
  Text,
  TextInput,
  Pressable,
  Platform,
  KeyboardAvoidingView,
  ActivityIndicator,
  Image,
  TouchableOpacity,
} from 'react-native';
import { type RouteProp, useNavigation, useRoute } from '@react-navigation/native';
import { ScrollView } from 'react-native-gesture-handler';
import ImagePicker from 'react-native-image-crop-picker';
import { RFPercentage } from 'react-native-responsive-fontsize';
import { useSelector } from 'react-redux';
import BackButton from '@/src/components/BackButton';
import BottomSheet from '@/src/components/Bottomsheet/index';
import Carousel from '@/src/components/Carousel';
import { OptionItem, OptionsMenu } from '@/src/components/OptionsMenu';
import SafeArea from '@/src/components/SafeArea';
import UserAvatar from '@/src/components/UserAvatar';
import { selectCurrentUser } from '@/src/redux/selectors/user';
import { formatMessageTime } from '@/src/utilities/datetime';
import type { HomeStackParamListI } from '@/src/navigation/types';
import Attachment from '@/src/assets/svgs/Attachment';
import Close from '@/src/assets/svgs/Close';
import Send from '@/src/assets/svgs/Send';
import EditMessageInput from './components/EditMessageInput';
import MoreOptions from './components/MoreOptions';
import OnlineIndicator from './components/OnlineIndicator';
import ReplyPreview from './components/ReplyPreview';
import SwipeableMessage from './components/SwipeableMessage';
import type { MediaPreviewItem, MessageI } from './types';
import { useChatScreenHook } from './useHook';

const ChatScreen = () => {
  const navigation = useNavigation();
  const route = useRoute<RouteProp<HomeStackParamListI, 'Chat'>>();
  const profileId = route.params.id;
  const currentUser = useSelector(selectCurrentUser);

  const [editingMessageId, setEditingMessageId] = useState<string | null>(null);
  const [editingText, setEditingText] = useState('');
  const [inputHeight, setInputHeight] = useState(40);
  const [mediaOptionsVisible, setMediaOptionsVisible] = useState(false);
  const [pendingAction, setPendingAction] = useState<'camera' | 'gallery' | null>(null);
  const [mediaPreview, setMediaPreview] = useState<MediaPreviewItem[]>([]);

  const textInputRef = useRef<TextInput>(null);
  const editInputRef = useRef<TextInput>(null);

  const {
    messages,
    profile,
    loading,
    loadingMore,
    hasMore,
    replyPreview,
    selectedMessage,
    optionsVisible,
    messageText,
    sending,
    scrollRef,
    isUserOnline,
    lastSeen,
    setMessageText,
    sendMessage,
    sendMediaMessage,
    uploadMedia,
    editMessage,
    handleLoadMore,
    handleMessageLongPress,
    handleReply,
    handleSwipeReply,
    handleCloseOptions,
    handleCloseReply,
    handleDeleteForMe,
    handleDeleteForEveryone,
    setOptionsVisible,
    setSelectedMessage,
  } = useChatScreenHook(profileId);

  const onBack = () => {
    navigation.goBack();
  };

  const handleAttachment = () => {
    setMediaOptionsVisible(true);
  };

  const handleCameraPress = () => {
    setPendingAction('camera');
    setMediaOptionsVisible(false);
  };

  const handleGalleryPress = () => {
    setPendingAction('gallery');
    setMediaOptionsVisible(false);
  };

  const handleModalHide = () => {
    if (!pendingAction) return;

    setTimeout(() => {
      if (pendingAction === 'camera') {
        openCamera();
      } else if (pendingAction === 'gallery') {
        openGallery();
      }
      setPendingAction(null);
    }, 300);
  };

  const openCamera = () => {
    ImagePicker.openCamera({
      width: 1024,
      height: 1024,
      cropping: false,
      mediaType: 'any',
      includeBase64: false,
    })
      .then((image) => {
        handleMediaSelection([image]);
      })
      .catch((error) => {
        if (error.code !== 'E_PICKER_CANCELLED') {
          console.error('Camera error:', error);
        }
      });
  };

  const openGallery = () => {
    ImagePicker.openPicker({
      multiple: true,
      mediaType: 'any',
      includeBase64: false,
      maxFiles: 5,
    })
      .then((images) => {
        handleMediaSelection(Array.isArray(images) ? images : [images]);
      })
      .catch((error) => {
        if (error.code !== 'E_PICKER_CANCELLED') {
          console.error('Gallery error:', error);
        }
      });
  };

  const handleMediaSelection = async (selectedMedia: any[]) => {
    const tempMediaItems: MediaPreviewItem[] = selectedMedia.map((item, index) => ({
      url: item.path,
      mimeType: item.mime,
      name: item.filename || `media_${index}.${item.mime.split('/')[1]}`,
      isUploading: true,
    }));

    setMediaPreview(tempMediaItems);

    try {
      const uploadedMedia = await uploadMedia(selectedMedia);
      setMediaPreview(uploadedMedia);
    } catch (error) {
      console.error('Failed to upload media:', error);
      setMediaPreview([]);
    }
  };

  const handleSendMedia = () => {
    if (mediaPreview.length === 0 || mediaPreview.some((item) => item.isUploading)) return;

    console.log(mediaPreview);

    sendMediaMessage(mediaPreview, messageText.trim());
    setMessageText('');
    setMediaPreview([]);
  };

  const handleCancelMediaPreview = () => {
    setMediaPreview([]);
  };

  const getDateLabel = (timestamp: Date): string => {
    const messageDate = new Date(timestamp);
    const today = new Date();
    const yesterday = new Date(today);
    yesterday.setDate(yesterday.getDate() - 1);

    const messageDateOnly = new Date(
      messageDate.getFullYear(),
      messageDate.getMonth(),
      messageDate.getDate(),
    );
    const todayOnly = new Date(today.getFullYear(), today.getMonth(), today.getDate());
    const yesterdayOnly = new Date(
      yesterday.getFullYear(),
      yesterday.getMonth(),
      yesterday.getDate(),
    );

    if (messageDateOnly.getTime() === todayOnly.getTime()) {
      return 'Today';
    } else if (messageDateOnly.getTime() === yesterdayOnly.getTime()) {
      return 'Yesterday';
    } else {
      return messageDate.toLocaleDateString('en-US', {
        weekday: 'long',
        month: 'short',
        day: 'numeric',
      });
    }
  };

  const groupMessagesByDate = (messages: MessageI[]) => {
    const groups: { [key: string]: MessageI[] } = {};

    const sortedMessages = [...messages].sort(
      (a, b) => new Date(a.createdAt).getTime() - new Date(b.createdAt).getTime(),
    );

    sortedMessages.forEach((message) => {
      const dateLabel = getDateLabel(message.createdAt);
      if (!groups[dateLabel]) {
        groups[dateLabel] = [];
      }
      groups[dateLabel].push(message);
    });

    return groups;
  };

  const canEditMessage = (message: MessageI): boolean => {
    if (message.senderId !== currentUser.profileId || message.deletedForAll) return false;
    const editTimeLimit = new Date(message.createdAt);
    editTimeLimit.setMinutes(editTimeLimit.getMinutes() + 15);
    return new Date() <= editTimeLimit;
  };

  const canDeleteForEveryone = (message: MessageI): boolean => {
    if (message.senderId !== currentUser.profileId || message.deletedForAll) return false;
    const deleteTimeLimit = new Date(message.createdAt);
    deleteTimeLimit.setMinutes(deleteTimeLimit.getMinutes() + 30);
    return new Date() <= deleteTimeLimit;
  };

  const handleEdit = () => {
    if (selectedMessage) {
      setEditingMessageId(selectedMessage.id);
      setEditingText(selectedMessage.content.text || '');
      setTimeout(() => {
        editInputRef.current?.focus();
      }, 100);
    }
    setOptionsVisible(false);
    setSelectedMessage(null);
  };

  const handleSaveEdit = () => {
    if (editingMessageId && editingText.trim()) {
      editMessage(editingMessageId, editingText.trim());
      setEditingMessageId(null);
      setEditingText('');
    }
  };

  const handleCancelEdit = () => {
    setEditingMessageId(null);
    setEditingText('');
  };

  const handleTextInputChange = (text: string) => {
    setMessageText(text);
  };

  const handleContentSizeChange = (event: any) => {
    const newHeight = Math.min(Math.max(40, event.nativeEvent.contentSize.height), 120);
    setInputHeight(newHeight);
  };

  const getOnlineStatus = () => {
    const now = new Date();

    if (isUserOnline && lastSeen) {
      const diffMinutes = Math.floor((now.getTime() - lastSeen.getTime()) / (1000 * 60));
      if (diffMinutes < 5) return 'online';
      if (diffMinutes < 60) return `last seen ${diffMinutes}m ago`;
      if (diffMinutes < 1440) return `last seen ${Math.floor(diffMinutes / 60)}h ago`;
      return `last seen ${Math.floor(diffMinutes / 1440)}d ago`;
    }

    if (isUserOnline) return 'online';

    if (lastSeen) {
      const diffMinutes = Math.floor((now.getTime() - lastSeen.getTime()) / (1000 * 60));
      if (diffMinutes < 1) return 'online';
      if (diffMinutes < 60) return `last seen ${diffMinutes}m ago`;
      if (diffMinutes < 1440) return `last seen ${Math.floor(diffMinutes / 60)}h ago`;
      return `last seen ${Math.floor(diffMinutes / 1440)}d ago`;
    }

    return 'offline';
  };

  const handleScroll = (event: any) => {
    const { contentOffset } = event.nativeEvent;
    if (contentOffset.y <= 0 && hasMore && !loadingMore) {
      handleLoadMore();
    }
  };

  const renderMediaCarousel = (mediaItems: any[], isMyMessage: boolean) => {
    if (!mediaItems || mediaItems.length === 0) return null;

    if (mediaItems.length === 1) {
      const mediaItem = mediaItems[0];
      return (
        <View className="rounded-lg overflow-hidden mb-2" style={{ minWidth: 200, maxWidth: 280 }}>
          {mediaItem.mimeType.startsWith('image/') || mediaItem.mimeType === 'JPEG' ? (
            <Image
              source={{ uri: mediaItem.url }}
              style={{ width: '100%', height: 200, minWidth: 200 }}
              resizeMode="cover"
            />
          ) : (
            <View className="w-full p-4 bg-gray-200 rounded-lg" style={{ minHeight: 120 }}>
              <Text className={`${isMyMessage ? 'text-white' : 'text-gray-600'} text-center`}>
                📎 {mediaItem.name || 'File'}
              </Text>
            </View>
          )}
        </View>
      );
    }

    const carouselItems = mediaItems.map((mediaItem, index) => (
      <View
        key={index}
        className="rounded-lg overflow-hidden"
        style={{ minWidth: 200, maxWidth: 280 }}
      >
        {mediaItem.mimeType.startsWith('image/') || mediaItem.mimeType === 'JPEG' ? (
          <Image
            source={{ uri: mediaItem.url }}
            style={{ width: '100%', height: 200, minWidth: 200 }}
            resizeMode="cover"
          />
        ) : (
          <View className="w-full p-4 bg-gray-200 rounded-lg h-48 justify-center items-center">
            <Text className={`${isMyMessage ? 'text-white' : 'text-gray-600'} text-center`}>
              📎 {mediaItem.name || 'File'}
            </Text>
          </View>
        )}
      </View>
    ));

    return (
      <View className="mb-2" style={{ minWidth: 200, maxWidth: 280 }}>
        <Carousel
          showDots={true}
          showArrows={false}
          showSlideNumbers={true}
          activeColor={isMyMessage ? '#ffffff' : '#7ba155'}
          inactiveColor={isMyMessage ? '#ffffff80' : '#D1FAE5'}
        >
          {carouselItems}
        </Carousel>
      </View>
    );
  };

  const renderMediaPreview = () => {
    if (mediaPreview.length === 0) return null;

    if (mediaPreview.length === 1) {
      const mediaItem = mediaPreview[0];
      return (
        <View className="rounded-lg overflow-hidden mb-2 relative">
          {mediaItem.mimeType.startsWith('image/') || mediaItem.mimeType === 'JPEG' ? (
            <Image source={{ uri: mediaItem.url }} className="w-full h-48" resizeMode="cover" />
          ) : (
            <View className="w-full p-3 bg-gray-200 rounded-lg h-48 justify-center items-center">
              <Text className="text-gray-600 text-center">📎 {mediaItem.name || 'File'}</Text>
            </View>
          )}
          {mediaItem.isUploading && (
            <View className="absolute inset-0 bg-black/50 justify-center items-center">
              <ActivityIndicator size="large" color="#ffffff" />
              <Text className="text-white mt-2">Uploading...</Text>
            </View>
          )}
          <TouchableOpacity
            className="absolute top-2 right-2 bg-black/50 rounded-full p-1"
            onPress={handleCancelMediaPreview}
          >
            <Close color="#ffffff" width={2} height={2} />
          </TouchableOpacity>
        </View>
      );
    }

    const previewItems = mediaPreview.map((mediaItem, index) => (
      <View key={index} className="rounded-lg overflow-hidden relative">
        {mediaItem.mimeType.startsWith('image/') || mediaItem.mimeType === 'JPEG' ? (
          <Image source={{ uri: mediaItem.url }} className="w-full h-48" resizeMode="cover" />
        ) : (
          <View className="w-full p-3 bg-gray-200 rounded-lg h-48 justify-center items-center">
            <Text className="text-gray-600 text-center">📎 {mediaItem.name || 'File'}</Text>
          </View>
        )}
        {mediaItem.isUploading && (
          <View className="absolute inset-0 bg-black/50 justify-center items-center">
            <ActivityIndicator size="large" color="#ffffff" />
            <Text className="text-white mt-2">Uploading...</Text>
          </View>
        )}
      </View>
    ));

    return (
      <View className="mb-2 relative">
        <Carousel
          showDots={true}
          showArrows={false}
          showSlideNumbers={true}
          className="h-48"
          activeColor="#7ba155"
          inactiveColor="#D1FAE5"
        >
          {previewItems}
        </Carousel>
        <TouchableOpacity
          className="absolute top-2 right-2 bg-black/50 rounded-full p-1 z-10"
          onPress={handleCancelMediaPreview}
        >
          <Close color="#ffffff" width={2} height={2} />
        </TouchableOpacity>
      </View>
    );
  };

  const getBottomSheetHeight = () => {
    return RFPercentage(18);
  };

  const isUploadingMedia = mediaPreview.some((item) => item.isUploading);

  if (loading) {
    return (
      <SafeArea>
        <View className="flex-1 justify-center items-center">
          <ActivityIndicator size="small" color="#448600" />
          <Text className="text-gray-600 mt-3 text-base">Loading chat...</Text>
        </View>
      </SafeArea>
    );
  }

  const messageGroups = groupMessagesByDate(messages);

  return (
    <SafeArea>
      <KeyboardAvoidingView
        className="flex-1"
        behavior={Platform.OS === 'ios' ? 'padding' : 'height'}
        keyboardVerticalOffset={Platform.OS === 'ios' ? 0 : 20}
      >
        <View className="flex-row items-center gap-3 px-4 py-4 bg-white shadow-sm border-b border-gray-100">
          <BackButton label="" onBack={onBack} />
          <View className="flex-row items-center gap-3 flex-1">
            <View className="relative">
              <UserAvatar avatarUri={profile?.avatar!} width={44} height={44} />
              <OnlineIndicator isOnline={isUserOnline} />
            </View>
            <View className="flex-1">
              <Text className="text-lg font-semibold text-gray-900">
                {profile?.name || 'Loading...'}
              </Text>
              <Text className="text-sm text-gray-500 mt-0.5">{getOnlineStatus()}</Text>
            </View>
          </View>
        </View>

        <ScrollView
          ref={scrollRef}
          className="flex-1 px-4 bg-gray-50"
          contentContainerStyle={{ paddingVertical: 16, flexGrow: 1 }}
          keyboardShouldPersistTaps="handled"
          onScroll={handleScroll}
          scrollEventThrottle={16}
          showsVerticalScrollIndicator={false}
          removeClippedSubviews={false}
          decelerationRate="normal"
          directionalLockEnabled={true}
          bounces={true}
          bouncesZoom={false}
          alwaysBounceVertical={false}
          canCancelContentTouches={false}
          scrollEnabled={true}
        >
          {loadingMore && (
            <View className="py-6 items-center">
              <ActivityIndicator size="small" color="#448600" />
            </View>
          )}

          <View className="gap-4">
            {Object.entries(messageGroups)
              .sort(([, messagesA], [, messagesB]) => {
                const earliestA = Math.min(
                  ...messagesA.map((m) => new Date(m.createdAt).getTime()),
                );
                const earliestB = Math.min(
                  ...messagesB.map((m) => new Date(m.createdAt).getTime()),
                );
                return earliestA - earliestB;
              })
              .map(([dateLabel, messagesInGroup]) => (
                <View key={dateLabel} className="gap-2">
                  <View className="bg-white rounded-full px-4 py-2 self-center shadow-sm">
                    <Text className="text-center text-gray-700 text-sm font-medium">
                      {dateLabel}
                    </Text>
                  </View>

                  <View className="gap-1">
                    {messagesInGroup.map((msg, index) => {
                      const time = formatMessageTime(new Date(msg.createdAt));
                      const isMyMessage = msg.senderId === currentUser.profileId;
                      const replyToMessage = msg.replyTo
                        ? messages.find((m) => m.id === msg.replyTo)
                        : null;
                      const isEditing = editingMessageId === msg.id;
                      const nextMessage = messagesInGroup[index + 1];
                      const isLastInGroup = !nextMessage || nextMessage.senderId !== msg.senderId;
                      const hasMedia = msg.content.media && msg.content.media.length > 0;
                      const hasText = msg.content.text && msg.content.text.trim();

                      return (
                        <SwipeableMessage
                          key={msg.id}
                          message={msg}
                          onReply={handleSwipeReply}
                          onLongPress={handleMessageLongPress}
                        >
                          <View
                            className={`${isMyMessage ? 'self-end' : 'self-start'} ${
                              isLastInGroup ? 'mb-2' : ''
                            }`}
                            style={{
                              maxWidth: hasMedia ? '85%' : '80%',
                              minWidth: hasMedia ? 220 : 'auto',
                            }}
                          >
                            <View
                              className={`px-4 py-3 rounded-2xl ${
                                isMyMessage
                                  ? 'bg-[#7ba155] rounded-br-md shadow-sm'
                                  : 'bg-white rounded-bl-md shadow-sm border border-gray-100'
                              } ${msg.deletedForAll ? 'opacity-60' : ''}`}
                            >
                              {replyToMessage && (
                                <View className="mb-3 p-3 bg-black/10 rounded-xl border-l-4 border-white">
                                  <Text
                                    className={`text-xs font-semibold ${
                                      isMyMessage ? 'text-white/90' : 'text-[#7ba155]'
                                    }`}
                                  >
                                    {replyToMessage.senderId === currentUser.profileId
                                      ? 'You'
                                      : profile?.name || 'User'}
                                  </Text>
                                  <Text
                                    className={`text-xs mt-1 ${isMyMessage ? 'text-white/80' : 'text-gray-600'}`}
                                    numberOfLines={2}
                                  >
                                    {replyToMessage.deletedForAll
                                      ? 'This message has been deleted'
                                      : replyToMessage.content.text || 'Media message'}
                                  </Text>
                                </View>
                              )}

                              {isEditing ? (
                                <EditMessageInput
                                  ref={editInputRef}
                                  value={editingText}
                                  onChangeText={setEditingText}
                                  onSave={handleSaveEdit}
                                  onCancel={handleCancelEdit}
                                />
                              ) : (
                                <>
                                  {hasMedia &&
                                    renderMediaCarousel(msg.content.media as any, isMyMessage)}

                                  {hasText && (
                                    <Text
                                      className={`text-base leading-6 ${
                                        msg.deletedForAll
                                          ? 'italic text-gray-500'
                                          : isMyMessage
                                            ? 'text-white'
                                            : 'text-gray-900'
                                      } ${hasMedia ? 'mt-2' : ''}`}
                                    >
                                      {msg.deletedForAll
                                        ? 'This message has been deleted'
                                        : msg.content.text}
                                    </Text>
                                  )}

                                  <View className="flex-row items-center justify-end gap-2 mt-2">
                                    {msg.editedAt && !msg.deletedForAll && (
                                      <Text
                                        className={`text-xs ${isMyMessage ? 'text-white/70' : 'text-gray-500'}`}
                                      >
                                        edited
                                      </Text>
                                    )}
                                    <Text
                                      className={`text-xs ${isMyMessage ? 'text-white/70' : 'text-gray-500'}`}
                                    >
                                      {time}
                                    </Text>
                                  </View>
                                </>
                              )}
                            </View>
                          </View>
                        </SwipeableMessage>
                      );
                    })}
                  </View>
                </View>
              ))}
          </View>
        </ScrollView>

        <View className="bg-white border-t border-gray-100 shadow-sm">
          {replyPreview && <ReplyPreview message={replyPreview} onClose={handleCloseReply} />}

          {mediaPreview.length > 0 && (
            <View className="px-4 pt-3">
              {renderMediaPreview()}
              <Text className="text-sm text-gray-500 mb-2">Add a caption (optional)</Text>
            </View>
          )}

          <View className="flex-row items-end px-4 py-4 gap-3">
            <Pressable
              className="w-12 h-12 rounded-full items-center justify-center bg-gray-100"
              onPress={handleAttachment}
              disabled={sending || mediaPreview.length > 0}
            >
              <Attachment color="#448600" width={3} height={3} />
            </Pressable>

            <View
              className="flex-1 bg-gray-100 rounded-3xl px-5 shadow-sm"
              style={{ minHeight: Math.max(48, inputHeight + 8) }}
            >
              <TextInput
                ref={textInputRef}
                value={messageText}
                onChangeText={handleTextInputChange}
                onContentSizeChange={handleContentSizeChange}
                placeholder={
                  mediaPreview.length > 0
                    ? 'Add a caption...'
                    : replyPreview
                      ? 'Reply to message...'
                      : 'Type a message...'
                }
                placeholderTextColor="#9CA3AF"
                className="text-base text-gray-900 leading-6 py-3"
                multiline
                textAlignVertical="top"
                style={{
                  maxHeight: 120,
                  minHeight: 24,
                }}
                editable={!sending}
                scrollEnabled={inputHeight >= 120}
              />
            </View>

            <Pressable
              className={`w-12 h-12 rounded-full items-center justify-center shadow-md ${
                (mediaPreview.length > 0 && !isUploadingMedia) || messageText.trim()
                  ? 'bg-[#448600]'
                  : 'bg-gray-400'
              } ${sending || isUploadingMedia ? 'opacity-60' : ''}`}
              onPress={mediaPreview.length > 0 ? handleSendMedia : sendMessage}
              disabled={
                sending || isUploadingMedia || (!messageText.trim() && mediaPreview.length === 0)
              }
            >
              {sending || isUploadingMedia ? (
                <ActivityIndicator size="small" color="white" />
              ) : (
                <Send color="white" width={2} height={2} />
              )}
            </Pressable>
          </View>
        </View>
      </KeyboardAvoidingView>

      <MoreOptions
        onClose={handleCloseOptions}
        optionsVisible={optionsVisible}
        onDeleteForMe={handleDeleteForMe}
        onDeleteForEveryone={handleDeleteForEveryone}
        onReply={handleReply}
        onEdit={handleEdit}
        canEdit={selectedMessage ? canEditMessage(selectedMessage) : false}
        canDeleteForEveryone={selectedMessage ? canDeleteForEveryone(selectedMessage) : false}
      />

      <BottomSheet
        onModalHide={handleModalHide}
        height={getBottomSheetHeight()}
        visible={mediaOptionsVisible}
        onClose={() => setMediaOptionsVisible(false)}
      >
        <OptionsMenu>
          <OptionItem label="Camera" onPress={handleCameraPress} />
          <View className="h-[1px] bg-gray-200" />
          <OptionItem label="Gallery" onPress={handleGalleryPress} />
        </OptionsMenu>
      </BottomSheet>
    </SafeArea>
  );
};

export default ChatScreen;
