import { useState, useEffect, useRef } from 'react';
import { useSelector } from 'react-redux';
import { selectCurrentUser } from '@/src/redux/selectors/user';
import { uploadFileWithPresignedUrl } from '@/src/utilities/upload/upload';
import { findAllSpecificProfileChats } from '@/src/networks/chat/individual';
import { useChatSocket } from '@/src/networks/chat/socket';
import type { MessageData } from '@/src/networks/chat/types';
import { fetchProfileAPI } from '@/src/networks/profile/userProfile';
import { fetchPresignedUrlAPI } from '@/src/networks/storage/presignedUrl';
import type { MediaPreviewItem, MessageI, ProfileData } from './types';

export const useChatScreenHook = (profileId: string) => {
  const [messages, setMessages] = useState<MessageI[]>([]);
  const [profile, setProfile] = useState<ProfileData | null>(null);
  const [loading, setLoading] = useState(true);
  const [loadingMore, setLoadingMore] = useState(false);
  const [hasMore, setHasMore] = useState(true);
  const [nextCursorId, setNextCursorId] = useState<string | null>(null);
  const [replyPreview, setReplyPreview] = useState<MessageI | null>(null);
  const [selectedMessage, setSelectedMessage] = useState<MessageI | null>(null);
  const [optionsVisible, setOptionsVisible] = useState(false);
  const [messageText, setMessageText] = useState('');
  const [sending, setSending] = useState(false);
  const [pendingMessages, setPendingMessages] = useState<Map<string, MessageI>>(new Map());
  const [isUserOnline, setIsUserOnline] = useState(false);
  const [lastSeen, setLastSeen] = useState<Date | null>(null);

  const currentUser = useSelector(selectCurrentUser);
  const scrollRef = useRef<any>(null);
  const initializationRef = useRef(false);

  const {
    connect,
    disconnect,
    sendMessage: socketSend,
    onMessage,
    removeMessageHandler,
    isConnected,
    isConnecting,
  } = useChatSocket(currentUser.profileId);

  const defaultUserProfile = {
    name: 'User',
    profileId,
    email: '',
    username: '',
    avatar: null,
    designation: null,
    entity: null,
  };

  const currentUserProfile = {
    name: 'You',
    profileId: currentUser.profileId,
    email: '',
    username: '',
    avatar: null,
    designation: null,
    entity: null,
  };

  const loadMessages = async (cursor?: string) => {
    try {
      if (cursor) {
        setLoadingMore(true);
      } else {
        setLoading(true);
      }

      const response = await findAllSpecificProfileChats({
        profileId,
        cursorId: cursor || null,
        pageSize: 10,
      });

      const messagesWithUser: MessageI[] = response.data.map((msg: MessageData) => ({
        ...msg,
        user:
          msg.senderId === currentUser.profileId
            ? currentUserProfile
            : profile || defaultUserProfile,
      }));

      if (cursor) {
        setMessages((prev) => [...messagesWithUser, ...prev]);
      } else {
        setMessages(messagesWithUser);
      }

      setNextCursorId(response.nextCursorId);
      setHasMore(response.hasMore);
    } catch (err) {
      console.error('Failed to load messages:', err);
    } finally {
      setLoading(false);
      setLoadingMore(false);
    }
  };

  const loadProfile = async () => {
    try {
      const profileData = await fetchProfileAPI(profileId);
      setProfile(profileData);
    } catch (err) {
      console.error('Failed to load profile:', err);
    }
  };

  const initializeSocket = async () => {
    if (initializationRef.current || isConnected || isConnecting) {
      return;
    }

    try {
      initializationRef.current = true;
      await connect();
    } catch (error) {
      console.error('[ChatHook] Failed to connect socket:', error);
      initializationRef.current = false;
    }
  };

  const handleLoadMore = () => {
    if (!loadingMore && hasMore && nextCursorId) {
      loadMessages(nextCursorId);
    }
  };

  const uploadMedia = async (selectedMedia: any[]): Promise<MediaPreviewItem[]> => {
    const extensions = selectedMedia.map((file) => file.mime.split('/')[1]).filter((ext) => ext);

    if (extensions.length === 0) {
      throw new Error('No valid media files');
    }

    const response = await fetchPresignedUrlAPI(extensions, 'POST');

    if (!Array.isArray(response) || response.length !== selectedMedia.length) {
      throw new Error('Failed to get upload URLs');
    }

    await Promise.all(
      selectedMedia.map((file, index) => {
        const presignedData = response[index];
        const fileBlob = {
          uri: file.path,
          type: file.mime,
          filename: file.filename || `media_${index}.${file.mime.split('/')[1]}`,
        };
        return uploadFileWithPresignedUrl(fileBlob, presignedData.uploadUrl);
      }),
    );

    return response.map((item, index) => ({
      url: item.accessUrl,
      mimeType: selectedMedia[index].mime,
      name:
        selectedMedia[index].filename ||
        `media_${index}.${selectedMedia[index].mime.split('/')[1]}`,
    }));
  };

  const sendMessage = async () => {
    if (!messageText.trim() || sending || !isConnected) return;

    const tempId = `temp_${Date.now()}`;
    const currentMessageText = messageText.trim();
    const currentReplyTo = replyPreview?.id || null;

    try {
      setSending(true);

      const tempMessage: MessageI = {
        id: tempId,
        senderId: currentUser.profileId,
        recieverId: profileId,
        content: { text: currentMessageText, media: [] },
        messageType: 'TEXT',
        replyTo: currentReplyTo,
        createdAt: new Date(),
        deletedForAll: false,
        deletedFor: [],
        user: currentUserProfile,
      };

      setMessages((prev) => [...prev, tempMessage]);
      setPendingMessages((prev) => new Map(prev.set(tempId, tempMessage)));
      setMessageText('');
      setReplyPreview(null);

      setTimeout(() => {
        scrollRef.current?.scrollToEnd({ animated: true });
      }, 100);

      socketSend('individual', {
        senderId: currentUser.profileId,
        recieverId: profileId,
        content: {
          text: currentMessageText,
          media: [],
        },
        replyTo: currentReplyTo,
        profileId: currentUser.profileId,
      });
    } catch (err) {
      console.error('[ChatHook] Error sending message:', err);
      setMessages((prev) => prev.filter((msg) => msg.id !== tempId));
      setPendingMessages((prev) => {
        const newMap = new Map(prev);
        newMap.delete(tempId);
        return newMap;
      });
    } finally {
      setSending(false);
    }
  };

  const sendMediaMessage = async (mediaItems: MediaPreviewItem[], captionText?: string) => {
    if (!isConnected || sending || mediaItems.length === 0) {
      return;
    }

    const tempId = `temp_${Date.now()}`;
    const currentReplyTo = replyPreview?.id || null;

    try {
      setSending(true);
      const tempMessage: MessageI = {
        id: tempId,
        senderId: currentUser.profileId,
        recieverId: profileId,
        content: {
          text: captionText || null,
          media: mediaItems,
        },
        messageType: 'MEDIA',
        replyTo: currentReplyTo,
        createdAt: new Date(),
        deletedForAll: false,
        deletedFor: [],
        user: currentUserProfile,
      };
      setMessages((prev) => {
        const newMessages = [...prev, tempMessage];
        return newMessages;
      });
      setPendingMessages((prev) => {
        const newMap = new Map(prev.set(tempId, tempMessage));
        return newMap;
      });
      if (replyPreview) {
        setReplyPreview(null);
      }
      setTimeout(() => {
        scrollRef.current?.scrollToEnd({ animated: true });
      }, 100);
      const socketPayload = {
        senderId: currentUser.profileId,
        recieverId: profileId,
        content: {
          text: captionText || null,
          media: mediaItems.map((item) => {
            const mimeType = item.mimeType.toLowerCase();
            let normalizedMimeType = mimeType;

            if (mimeType.startsWith('image/')) {
              normalizedMimeType = 'JPEG';
            } else if (mimeType === 'application/pdf' || mimeType.includes('pdf')) {
              normalizedMimeType = 'PDF';
            } else if (mimeType === 'text/plain') {
              normalizedMimeType = 'TXT';
            }

            return {
              ...item,
              mimeType: normalizedMimeType,
            };
          }),
        },
        replyTo: currentReplyTo,
        profileId: currentUser.profileId,
      };
      socketSend('individual', socketPayload);
    } catch (err) {
      setMessages((prev) => {
        const filteredMessages = prev.filter((msg) => msg.id !== tempId);
        return filteredMessages;
      });

      setPendingMessages((prev) => {
        const newMap = new Map(prev);
        newMap.delete(tempId);
        return newMap;
      });
    } finally {
      setSending(false);
    }
  };

  const editMessage = async (messageId: string, newContent: string) => {
    try {
      socketSend('edit-message', {
        id: messageId,
        senderId: currentUser.profileId,
        recieverId: profileId,
        content: {
          text: newContent,
          media: [],
        },
        profileId: currentUser.profileId,
      });

      setMessages((prev) =>
        prev.map((msg) =>
          msg.id === messageId
            ? { ...msg, content: { ...msg.content, text: newContent }, editedAt: new Date() }
            : msg,
        ),
      );
    } catch (err) {
      console.error('[ChatHook] Error editing message:', err);
    }
  };

  const deleteForMe = async (messageId: string) => {
    try {
      socketSend('delete-for-me', {
        ids: [messageId],
        senderId: currentUser.profileId,
        recieverId: profileId,
        profileId: currentUser.profileId,
      });

      setMessages((prev) => prev.filter((msg) => msg.id !== messageId));
    } catch (err) {
      console.error('[ChatHook] Error deleting message for me:', err);
    }
  };

  const deleteForEveryone = async (messageId: string) => {
    try {
      socketSend('delete-for-everyone', {
        id: messageId,
        senderId: currentUser.profileId,
        recieverId: profileId,
        profileId: currentUser.profileId,
      });

      setMessages((prev) =>
        prev.map((msg) =>
          msg.id === messageId
            ? {
                ...msg,
                deletedForAll: true,
                content: { ...msg.content, text: 'This message has been deleted' },
              }
            : msg,
        ),
      );
    } catch (err) {
      console.error('[ChatHook] Error deleting message for everyone:', err);
    }
  };

  const handleMessageLongPress = (message: MessageI) => {
    setSelectedMessage(message);
    setOptionsVisible(true);
  };

  const handleReply = () => {
    if (selectedMessage) {
      setReplyPreview(selectedMessage);
    }
    setOptionsVisible(false);
    setSelectedMessage(null);
  };

  const handleSwipeReply = (message: MessageI) => {
    setReplyPreview(message);
  };

  const handleCloseOptions = () => {
    setOptionsVisible(false);
    setSelectedMessage(null);
  };

  const handleCloseReply = () => {
    setReplyPreview(null);
  };

  const handleDeleteForMe = () => {
    if (selectedMessage) {
      deleteForMe(selectedMessage.id);
      setOptionsVisible(false);
      setSelectedMessage(null);
    }
  };

  const handleDeleteForEveryone = () => {
    if (selectedMessage) {
      deleteForEveryone(selectedMessage.id);
      setOptionsVisible(false);
      setSelectedMessage(null);
    }
  };

  const socketHandlers = {
    handleIndividualMessage: (data: any) => {
      const messageWithUser: MessageI = {
        ...data,
        user:
          data.senderId === currentUser.profileId
            ? currentUserProfile
            : profile || defaultUserProfile,
      };

      const tempMessage = Array.from(pendingMessages.values()).find(
        (msg) =>
          msg.senderId === data.senderId &&
          msg.recieverId === data.recieverId &&
          (msg.content.text === data.content.text || (msg.content.media && data.content.media)),
      );

      if (tempMessage) {
        setMessages((prev) =>
          prev.map((msg) => (msg.id === tempMessage.id ? messageWithUser : msg)),
        );
        setPendingMessages((prev) => {
          const newMap = new Map(prev);
          newMap.delete(tempMessage.id);
          return newMap;
        });
      } else {
        setMessages((prev) => [...prev, messageWithUser]);
      }

      setTimeout(() => {
        scrollRef.current?.scrollToEnd({ animated: true });
      }, 100);
    },

    handleMessageError: (data: any) => {
      const failedMessage = Array.from(pendingMessages.values()).find(
        (msg) =>
          msg.senderId === data.originalSenderId && msg.recieverId === data.originalRecieverId,
      );

      if (failedMessage) {
        setMessages((prev) => prev.filter((msg) => msg.id !== failedMessage.id));
        setPendingMessages((prev) => {
          const newMap = new Map(prev);
          newMap.delete(failedMessage.id);
          return newMap;
        });
      }
    },

    handleMessagesDeleted: (data: { ids: string[]; senderId: string }) => {
      setMessages((prev) => prev.filter((msg) => !data.ids.includes(msg.id)));
    },

    handleMessageDeleted: (data: { id: string; senderId: string }) => {
      setMessages((prev) =>
        prev.map((msg) =>
          msg.id === data.id
            ? {
                ...msg,
                deletedForAll: true,
                content: { ...msg.content, text: 'This message has been deleted' },
              }
            : msg,
        ),
      );
    },

    handleMessageEdited: (data: { id: string; content: any; senderId: string }) => {
      setMessages((prev) =>
        prev.map((msg) =>
          msg.id === data.id ? { ...msg, content: data.content, editedAt: new Date() } : msg,
        ),
      );
    },

    handleUserStatus: (data: { profileId: string; status: string; lastSeen: string }) => {
      if (data.profileId !== profileId) return;
      setIsUserOnline(data.status === 'online');
      setLastSeen(data.status === 'offline' ? new Date(data.lastSeen) : null);
    },
  };

  useEffect(() => {
    const initializeChat = async () => {
      try {
        await Promise.all([loadProfile(), loadMessages(), initializeSocket()]);
      } catch (error) {
        console.error('Failed to initialize chat:', error);
        setLoading(false);
      }
    };

    initializeChat();
  }, [profileId]);

  useEffect(() => {
    if (!isConnected) return;

    const handlers = socketHandlers;

    onMessage('individual', handlers.handleIndividualMessage);
    onMessage('message-error', handlers.handleMessageError);
    onMessage('messages-deleted', handlers.handleMessagesDeleted);
    onMessage('message-deleted', handlers.handleMessageDeleted);
    onMessage('message-edited', handlers.handleMessageEdited);
    onMessage('user-status', handlers.handleUserStatus);

    return () => {
      removeMessageHandler('individual');
      removeMessageHandler('message-error');
      removeMessageHandler('messages-deleted');
      removeMessageHandler('message-deleted');
      removeMessageHandler('message-edited');
      removeMessageHandler('messages-read');
      removeMessageHandler('read-receipt');
      removeMessageHandler('user-status');
    };
  }, [isConnected, pendingMessages, profile]);

  useEffect(() => {
    return () => {
      disconnect();
    };
  }, []);

  return {
    messages,
    profile,
    loading,
    loadingMore,
    hasMore,
    replyPreview,
    selectedMessage,
    optionsVisible,
    messageText,
    sending,
    scrollRef,
    isConnected,
    isUserOnline,
    lastSeen,
    setMessageText,
    setOptionsVisible,
    setSelectedMessage,
    sendMessage,
    sendMediaMessage,
    uploadMedia,
    editMessage,
    deleteForMe,
    deleteForEveryone,
    handleLoadMore,
    handleMessageLongPress,
    handleReply,
    handleSwipeReply,
    handleCloseOptions,
    handleCloseReply,
    handleDeleteForMe,
    handleDeleteForEveryone,
  };
};
