/*
Copyright (c) 2025-present Navicater Solutions

This source code is licensed under the license found in the
LICENSE file in the root directory of this source tree.
*/
import React from 'react';
import { Image, Pressable, StatusBar, Text, View } from 'react-native';
import { useNavigation } from '@react-navigation/native';
import LinearGradient from 'react-native-linear-gradient';
import Button from '@/src/components/Button';
import { BottomTabNavigationI } from '@/src/navigation/types';
import ChevronLeft from '@/src/assets/svgs/ChevronLeft';
import { PortHeaderProps } from './types';

const PortHeader: React.FC<PortHeaderProps> = ({ imageUri, label }) => {
  const navigation = useNavigation<BottomTabNavigationI>();

  const handleBack = () => {
    navigation.goBack();
  };

  const handleViewMore = () => {};

  const handleContribute = () => {};

  return (
    <View className="relative h-96">
      <View className="absolute top-0 left-0 w-full h-full">
        <Image
          source={{
            uri:
              imageUri ??
              'https://img.freepik.com/free-photo/aerial-view-cargo-ship-cargo-container-harbor_335224-1380.jpg?semt=ais_hybrid&w=740',
          }}
          className="w-full h-full"
          resizeMode="cover"
        />
        <LinearGradient
          colors={['rgba(0, 0, 0, 0.7)', 'rgba(0, 0, 0, 0)']}
          locations={[0, 1]}
          className="absolute top-0 left-0 w-full h-1/3"
        />
      </View>
      <StatusBar barStyle="light-content" />
      <View className="flex-row justify-between items-center mt-10 px-4">
        <Pressable
          onPress={handleBack}
          className="rounded-full bg-black opacity-50"
          // style={{ mixBlendMode: 'difference' }}
        >
          <ChevronLeft stroke="#ffffff" />
        </Pressable>
        <Pressable
          onPress={handleViewMore}
          className="px-2 rounded-md bg-black opacity-50 p-1" /*style={{ mixBlendMode: 'difference' }}*/
        >
          <Text className="text-white text-sm underline">View all</Text>
        </Pressable>
      </View>
      <View className="absolute bottom-4 left-0 right-0 flex-row justify-between items-center px-4">
        <Text
          className="text-white text-xl leading-6 font-semibold max-w-[70%]"
          style={{ mixBlendMode: 'difference' }}
        >
          {label || '-'}
        </Text>
        {/* <View style={{ mixBlendMode: 'difference' }}>
          <Button
            variant="outline"
            onPress={handleContribute}
            label="Contribute"
            className="rounded-lg w-fit border-white bg-transparent"
            labelClassName="text-white text-sm font-medium"
          />
        </View> */}
      </View>
    </View>
  );
};

export default PortHeader;
