import { useEffect, useState } from 'react';
import { useFocusEffect, useNavigation } from '@react-navigation/native';
import { StackNavigationProp } from '@react-navigation/stack';
import { Resolver, useFieldArray, useForm } from 'react-hook-form';
import { useDispatch, useSelector } from 'react-redux';
import { selectAllDesignations, selectExperiences } from '@/src/redux/selectors/experience';
import { selectAllSelections, selectSelectionByKey } from '@/src/redux/selectors/search';
import { clearSelection, clearSelectionAsync } from '@/src/redux/slices/entitysearch/searchSlice';
import { SearchResultI } from '@/src/redux/slices/entitysearch/types';
import {
  addExperienceAsync,
  addShipExperience,
} from '@/src/redux/slices/experience/experienceSlice';
import { AppDispatch, store } from '@/src/redux/store';
import { handleError } from '@/src/utilities/errors/errors';
import { navigate } from '@/src/utilities/navigation';
import { showToast } from '@/src/utilities/toast';
import { apiCall } from '@/src/services/api';
import { generateExperiencePayload } from '../utils';
import {
  apiResponseTypeI,
  DesignationsI,
  DesignationWithDateI,
  ExperienceFormDataI,
  FetchedExperienceI,
  FieldTypeI,
  UseEditExperienceItemI,
} from './types';

const useEditExperienceItem = (profileId: string, experienceId?: string): any => {
  const [loading, setLoading] = useState(false);
  const [isSubmitting, setIsSubmitting] = useState(false);
  const dispatch = useDispatch<AppDispatch>();
  const entity = useSelector(selectSelectionByKey('entity'));
  const designations = useSelector(selectAllDesignations);
  const isEdit = Boolean(experienceId);
  const [isDeleting, setIsDeleting] = useState(false);
  const [localDesignations, setLocalDesignations] = useState<DesignationWithDateI[]>([]);
  const [initialDesignations, setInitialDesignations] = useState<DesignationWithDateI[]>([]);
  const [localEntity, setLocalEntity] = useState<SearchResultI>();
  const [initialEntity, setInitialEntity] = useState<SearchResultI>();
  const [error, setError] = useState<Error | null>(null);

  const triggerErrorBoundary = (error: Error) => {
    setError(error);
  };

  if (error) {
    throw error;
  }

  const handleAddDesignation = () => {
    const newDesignation = {
      designation: {
        id: undefined,
        name: undefined,
        dataType: undefined,
      },
      fromDate: undefined,
      toDate: undefined,
      ships: [],
    };
    setLocalDesignations((prev) => [...prev, newDesignation]);
  };

  const handleDesignationChange = (index: number, selectedDesignation: SearchResultI) => {
    setLocalDesignations((prev) => {
      const updated = [...prev];
      updated[index] = {
        ...updated[index],
        designation: selectedDesignation,
      };
      return updated;
    });
  };

  const handleDateChange = (index: number, field: any, date: Date) => {
    if (date instanceof Date) {
      const dateString = date.toISOString().split('T')[0];
      setLocalDesignations((prev) => {
        const updated = [...prev];
        updated[index] = {
          ...updated[index],
          [field]: dateString,
        };
        return updated;
      });
    }
  };

  const fetchExperience = async (experienceId: string) => {
    try {
      const response: apiResponseTypeI = await apiCall(
        `/backend/api/v1/career/profile-experience/${experienceId}`,
        'GET',
        { isAuth: true },
      );
      setLocalEntity(response.entity);
      setInitialEntity(response.entity);
      setLocalDesignations(response.designations);
      setInitialDesignations(response.designations);
    } catch (error) {
      triggerErrorBoundary(
        new Error(
          'Failed to load experience details: ' +
            (error instanceof Error ? error.message : 'Unknown error'),
        ),
      );
    }
  };

  useEffect(() => {
    if (experienceId) {
      fetchExperience(experienceId);
    }
  }, [experienceId]);

  const refetch = () => {
    fetchExperience(experienceId!);
  };

  useEffect(() => {
    setLocalEntity(entity);
  }, [entity]);

  useEffect(() => {
    designations.map((designation) => {
      handleDesignationChange(Number(designation.indexKey), {
        id: designation.id,
        name: designation.name,
        dataType: designation.dataType,
      });
    });
  }, [designations.length]);

  const clearFields = async () => {
    await dispatch(clearSelectionAsync('entity'));
    const state = store.getState();
    const selections = selectAllSelections(state);
    Object.keys(selections).forEach(async (key) => {
      if (key.startsWith('designation')) {
        await dispatch(clearSelectionAsync(key));
      }
    });
  };

  const handleSubmit = async () => {
    const throwValidation = localDesignations.length <= 0 && !isEdit;
    if (throwValidation) {
      return showToast({
        message: 'Validation',
        description: 'Atleast one designation is needed',
        type: 'error',
      });
    }
    try {
      const payload = generateExperiencePayload(
        localEntity!,
        initialEntity!,
        localDesignations,
        initialDesignations,
        experienceId,
      );
      await dispatch(addExperienceAsync({ payload })).unwrap();
      showToast({
        message: 'Success',
        description: 'Experience updated successfully',
        type: 'success',
      });
      navigate('EditExperienceList', {
        profileId: profileId,
      });
    } catch (error) {
      showToast({
        message: 'Error',
        description: 'Failed to update experience',
        type: 'error',
      });
    } finally {
      setIsSubmitting(false);
      clearFields();
    }
  };

  const handleAddEditShip = (field: FieldTypeI, shipId?: string) => {
    const data = generateExperiencePayload(
      localEntity!,
      initialEntity!,
      localDesignations,
      initialDesignations,
      experienceId,
      field,
    );
    let entityId = localEntity?.id;

    navigate('EditShipItem', {
      data,
      field,
      shipId,
      entityId,
      refetch,
    });
  };

  const handleDeleteShip = async (field: FieldTypeI, shipId: string) => {
    try {
      setIsDeleting(true);
      const payload = [
        {
          id: experienceId,
          opr: 'NESTED_OPR' as 'UPDATE' | 'DELETE' | 'CREATE' | 'NESTED_OPR',
          designations: [
            {
              id: field.id,
              opr: 'NESTED_OPR',
              ships: [
                {
                  id: shipId,
                  opr: 'DELETE' as 'UPDATE' | 'DELETE' | 'CREATE' | 'NESTED_OPR',
                },
              ],
            },
          ],
        },
      ];
      await dispatch(addShipExperience({ payload })).unwrap();

      setLocalDesignations((prevDesignations) => {
        return prevDesignations.map((designation) => {
          if (designation.id === field.id) {
            const updatedShips = designation.ships?.filter((ship) => ship.id !== shipId) || [];
            return {
              ...designation,
              ships: updatedShips,
            };
          }
          return designation;
        });
      });
    } catch (error) {
      handleError(error, {
        handle4xxError: () => {
          showToast({
            message: 'Failed to delete ship',
            type: 'error',
          });
        },
        handle5xxError: () => {
          showToast({
            message: 'Server Error',
            description: 'Please try again later',
            type: 'error',
          });
        },
      });
    } finally {
      setIsDeleting(false);
    }
  };

  return {
    designations,
    isSubmitting,
    loading,
    handleSubmit,
    handleAddDesignation,
    handleAddEditShip,
    handleDeleteShip,
    isDeleting,
    localDesignations,
    handleDateChange,
    localEntity,
    setLocalDesignations,
  };
};

export default useEditExperienceItem;
