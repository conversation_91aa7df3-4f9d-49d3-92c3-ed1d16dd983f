/*
Copyright (c) 2025-present Navicater Solutions

This source code is licensed under the license found in the
LICENSE file in the root directory of this source tree.
*/
import { useNavigation } from '@react-navigation/native';
import BackButton from '@/src/components/BackButton';
import SafeArea from '@/src/components/SafeArea';
import NotificationList from './components/NotificationList';

const NotificationScreen = () => {
  const navigation = useNavigation();

  return (
    <SafeArea>
      <BackButton onBack={() => navigation.goBack()} />
      <NotificationList />
    </SafeArea>
  );
};

export default NotificationScreen;
