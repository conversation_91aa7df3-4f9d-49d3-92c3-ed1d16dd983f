/*
Copyright (c) 2025-present Navicater Solutions

This source code is licensed under the license found in the
LICENSE file in the root directory of this source tree.
*/
import CreateContentScreen from '@/src/screens/CreateContent';
import type { CreateStackParamsListI, StackScreenI } from '../../types';
import { withErrorBoundary } from '../../withErrorBoundary';

const CreateContentScreenWithErrorBoundary = withErrorBoundary(CreateContentScreen, {
  title: 'Content Creation Error',
  subtitle: 'Something went wrong while creating content. Please try again.',
});

export const screens: StackScreenI<CreateStackParamsListI>[] = [
  { name: 'CreateContent', component: CreateContentScreenWithErrorBoundary },
];
