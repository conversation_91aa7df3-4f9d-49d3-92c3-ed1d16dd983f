import { SearchResultI } from '@/src/redux/slices/entitysearch/types';
import { ObjUnknownI } from '@/src/types/common/data';

export interface FetchShipProfileQueryI extends ObjUnknownI {
  imo: string;
  dataType: string;
}

export interface NamedEntityI {
  name: string;
}

export interface ShipNameHistoryI {
  name: string;
  fromDate: string | null;
  toDate: string | null;
}

export interface FetchShipProfileResultI {
  imo: string;
  mmsi: number;
  callSign: string;
  name: string;
  imageUrl: string;
  yearBuilt: number;
  country: NamedEntityI;
  mainVesselType: NamedEntityI;
  subVesselTypes: NamedEntityI[];
  dataType: string;
  shipNames: ShipNameHistoryI[];
}

export type FetchShipPeopleQueryI = {
  imo: string;
  dataType: string;
};

export type FetchPeopleResult = {
  avatar: string;
  designation: SearchResultI;
  entity: SearchResultI;
  id: string;
  isConnected: boolean;
  name: string;
};

export interface CreateShipPayloadI {
  imo: string;
  name: string;
  mmsi?: string;
  callSign?: string;
  flagCountryIso2?: string;
  mainVesselTypeId?: string;
  mainVesselTypeRawDataId?: string;
  status: 'ACTIVE' | 'INACTIVE';
  portOfRegistry?: string;
  yearBuilt?: number;
}

export interface CreateShipResultI {
  imo: string;
  name: string;
  mmsi?: string;
  callSign?: string;
  dataType: 'raw' | 'master';
  id: string;
}
