import { apiCall } from '../../services/api';
import {
  FetchShipProfileQueryI,
  FetchShipProfileResultI,
  CreateShipPayloadI,
  CreateShipResultI,
} from './types';

export const fetchShipProfile = async (query: FetchShipProfileQueryI) => {
  const result = await apiCall<unknown, FetchShipProfileResultI>(
    '/backend/api/v1/ship/single',
    'GET',
    {
      isAuth: true,
      query,
    },
  );

  return result;
};

export const createShipAPI = async (payload: CreateShipPayloadI) => {
  const result = await apiCall<CreateShipPayloadI, CreateShipResultI>(
    '/backend/api/v1/ship/ship',
    'POST',
    {
      isAuth: true,
      payload,
    },
  );

  return result;
};
