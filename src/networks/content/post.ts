/*
Copyright (c) 2025-present Navicater Solutions

This source code is licensed under the license found in the
LICENSE file in the root directory of this source tree.
*/
import { GlobalSearchParams, GlobalSearchResponse } from '@/src/utilities/search/types';
import { apiCall } from '../../services/api';
import {
  GetPostsPayloadI,
  PostCreateOneI,
  PostCreateResponse,
  PostExternalClientI,
  PostFetchManyResultI,
} from './types';

export const fetchPostsAPI = async (query: GetPostsPayloadI) => {
  const result = await apiCall<unknown, PostFetchManyResultI>('/backend/api/v1/feed/posts', 'GET', {
    isAuth: true,
    query,
  });

  return result;
};

export const fetchPostAPI = async (id: string) => {
  const result = await apiCall<{ id: string }, PostExternalClientI>(
    `/backend/api/v1/feed/post/${id}`,
    'GET',
    {
      isAuth: true,
      query: {
        id,
      },
    },
  );

  return result;
};

export const addPostAPI = async (payload: PostCreateOneI): Promise<PostCreateResponse> => {
  const result = await apiCall<PostCreateOneI, PostCreateResponse>(
    '/backend/api/v1/feed/post',
    'POST',
    {
      isAuth: true,
      payload,
    },
  );

  return result;
};

export const editPostAPI = async (
  payload: PostCreateOneI,
  routeId: string,
): Promise<PostCreateResponse> => {
  const result = await apiCall<PostCreateOneI, PostCreateResponse>(
    '/backend/api/v1/feed/post',
    'PATCH',
    {
      isAuth: true,
      payload,
      routeId,
    },
  );

  return result;
};

export const fetchProfilePostsAPI = async (query: GetPostsPayloadI) => {
  const result = await apiCall<unknown, PostFetchManyResultI>(
    '/backend/api/v1/feed/profile/posts',
    'GET',
    {
      isAuth: true,
      query,
    },
  );

  return result;
};

export const deletePostAPI = async (routeId: string) => {
  const result = await apiCall<unknown, unknown>('/backend/api/v1/feed/post', 'DELETE', {
    isAuth: true,
    routeId,
  });

  return result;
};

export const searchPostGlobalAPI = async (query: GlobalSearchParams) => {
  const result = await apiCall<unknown, GlobalSearchResponse>(
    '/backend/api/v1/feed/post/global-search',
    'GET',
    {
      isAuth: true,
      query,
    },
  );

  return result;
};
