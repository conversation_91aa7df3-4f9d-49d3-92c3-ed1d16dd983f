/*
Copyright (c) 2025-present Navicater Solutions

This source code is licensed under the license found in the
LICENSE file in the root directory of this source tree.
*/
import { apiCall } from '../../services/api';
import {
  CommentCreateI,
  CommentCreateResponseI,
  GetCommentsPayloadI,
  GetCommentsRepliesPayloadI,
  GetCommentsResponseI,
} from './types';

export const addCommentAPI = async (payload: CommentCreateI): Promise<CommentCreateResponseI> => {
  const result = await apiCall<CommentCreateI, CommentCreateResponseI>(
    '/backend/api/v1/feed/comment',
    'POST',
    {
      isAuth: true,
      payload,
    },
  );

  return result;
};

export const fetchCommentsAPI = async (
  query: GetCommentsPayloadI,
): Promise<GetCommentsResponseI> => {
  const result = await apiCall<GetCommentsPayloadI, GetCommentsResponseI>(
    '/backend/api/v1/feed/comments',
    'GET',
    {
      query,
      isAuth: true,
    },
  );

  return result;
};

export const deleteCommentAPI = async (commentId: string): Promise<unknown> => {
  const result = await apiCall(`/backend/api/v1/feed/comment`, 'DELETE', {
    isAuth: true,
    routeId: commentId,
  });

  return result;
};

export const fetchCommentRepliesAPI = async (
  query: GetCommentsRepliesPayloadI,
): Promise<GetCommentsResponseI> => {
  const { postId, parentCommentId, cursorId, pageSize } = query;

  const result = await apiCall<unknown, GetCommentsResponseI>(
    `/backend/api/v1/feed/comment/${parentCommentId}/replies`,
    'GET',
    {
      isAuth: true,
      query: {
        postId,
        cursorId,
        pageSize,
      },
    },
  );

  return result;
};
