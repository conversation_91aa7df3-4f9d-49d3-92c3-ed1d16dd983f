import { apiCall } from '../../services/api';
import {
  AddDocumentBodyI,
  AddDocumentResultI,
  EditDocumentBodyI,
  FetchDocumentResultI,
  IdentityDocumentationI,
  VisaDocumentationI,
} from './types';

export const addDocumentAPI = async (
  payload: AddDocumentBodyI,
  type: 'identity' | 'visa' | 'medical',
): Promise<AddDocumentResultI> => {
  const route = `/backend/api/v1/document/${type}`;
  const result = await apiCall<AddDocumentBodyI, AddDocumentResultI>(route, 'POST', {
    isAuth: true,
    payload,
  });

  return result;
};

export const fetchDocumentAPI = async (
  documentId: string,
  type: string,
): Promise<FetchDocumentResultI> => {
  const route =
    type.toLowerCase() === 'identity'
      ? `/backend/api/v1/document/identity/${documentId}`
      : `/backend/api/v1/document/visa/${documentId}`;

  const result = await apiCall<string, FetchDocumentResultI>(route, 'GET', {
    isAuth: true,
  });

  return result;
};

export const deleteDocumentAPI = async (documentId: string, type: string) => {
  await apiCall<string>(`/backend/api/v1/document/${type}/${documentId}`, 'DELETE', {
    isAuth: true,
  });
};

export const fetchIdentityDocumentsAPI = async (
  profileId: string,
  page: number,
  pageSize: number,
): Promise<IdentityDocumentationI[]> => {
  const result = await apiCall<string, IdentityDocumentationI[]>(
    '/backend/api/v1/document/identities',
    'GET',
    {
      isAuth: true,
      query: {
        profileId,
        page,
        pageSize,
      },
    },
  );

  return result;
};

export const fetchVisaDocumentsAPI = async (
  profileId: string,
  page: number,
  pageSize: number,
): Promise<VisaDocumentationI[]> => {
  const result = await apiCall<string, VisaDocumentationI[]>(
    '/backend/api/v1/document/visas',
    'GET',
    {
      isAuth: true,
      query: {
        profileId,
        page,
        pageSize,
      },
    },
  );

  return result;
};

export const editIdentityDocumentAPI = async (
  payload: EditDocumentBodyI,
  documentId: string,
): Promise<void> => {
  const result = await apiCall<EditDocumentBodyI, void>(
    `/backend/api/v1/document/identity/${documentId}`,
    'PATCH',
    {
      isAuth: true,
      payload,
    },
  );

  return result;
};

export const editVisaDocumentAPI = async (
  payload: EditDocumentBodyI,
  documentId: string,
): Promise<void> => {
  const result = await apiCall<EditDocumentBodyI, void>(
    `/backend/api/v1/document/visa/${documentId}`,
    'PATCH',
    {
      isAuth: true,
      payload,
    },
  );

  return result;
};
