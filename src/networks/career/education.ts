import { apiCall } from '../../services/api';
import {
  AddEducationResultI,
  EditEducationBodyI,
  fetchedEducationTypeI,
  fetchEducation,
} from './types';

export const addEducationAPI = async (payload: AddEducationResultI): Promise<{ id: string }> => {
  const result = await apiCall<AddEducationResultI, { id: string }>(
    '/backend/api/v1/career/institute',
    'POST',
    {
      isAuth: true,
      payload,
    },
  );

  return result;
};

export const fetchEducationsAPI = async (
  profileId: string,
  page: number,
  pageSize: number,
): Promise<fetchedEducationTypeI[]> => {
  const result = await apiCall<string, fetchedEducationTypeI[]>(
    '/backend/api/v1/career/institutes',
    'GET',
    {
      isAuth: true,
      query: {
        profileId,
        page,
        pageSize,
      },
    },
  );

  return result;
};

export const fetchEducationAPI = async (educationId: string): Promise<fetchEducation> => {
  const result = await apiCall<string, fetchEducation>(
    `/backend/api/v1/career/institute/${educationId}`,
    'GET',
    {
      isAuth: true,
    },
  );

  return result;
};

export const deleteEducationAPI = async (educationId: string) => {
  await apiCall<string, fetchEducation>(
    `/backend/api/v1/career/institute/${educationId}`,
    'DELETE',
    {
      isAuth: true,
    },
  );
};

export const editEducationAPI = async (
  educationId: string,
  payload: EditEducationBodyI,
): Promise<void> => {
  const result = await apiCall<EditEducationBodyI, void>(
    `/backend/api/v1/career/institute/${educationId}`,
    'PATCH',
    {
      isAuth: true,
      payload,
    },
  );

  return result;
};
