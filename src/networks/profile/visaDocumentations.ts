import { apiCall } from '../../services/api';
import {
  FetchUserProfileVisaDocumentationsBodyI,
  FetchUserProfileVisaDocumentationsResultI,
} from './types';

export const fetchVisaDocumentationsAPI = async (
  profileId: string,
): Promise<FetchUserProfileVisaDocumentationsResultI[]> => {
  const result = await apiCall<
    FetchUserProfileVisaDocumentationsBodyI,
    FetchUserProfileVisaDocumentationsResultI[]
  >('/backend/api/v1/document/visas', 'GET', {
    isAuth: true,
    query: {
      profileId: profileId,
      pageSize: 3,
    },
  });

  return result;
};
