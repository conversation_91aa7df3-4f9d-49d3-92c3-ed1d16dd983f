/*
Copyright (c) 2025-present Navicater Solutions

This source code is licensed under the license found in the
LICENSE file in the root directory of this source tree.
*/
import { AboutProfileI } from '@/src/screens/UserProfile/components/ProfileContent/types';
import { apiCall } from '../../services/api';

export const fetchAboutProfileAPI = async (routeId: string): Promise<AboutProfileI> => {
  const result = await apiCall<unknown, AboutProfileI>(
    `/backend/api/v1/user/profile/about`,
    'GET',
    {
      isAuth: true,
      routeId,
    },
  );

  return result;
};
