/*
Copyright (c) 2025-present Navicater Solutions

This source code is licensed under the license found in the
LICENSE file in the root directory of this source tree.
*/
import { apiCall } from '../../services/api';
import { OnboardingWorkBodyI, OnboardingWorkResultI } from './types';

export const onboardingWorkAPI = async (
  payload: OnboardingWorkBodyI,
): Promise<OnboardingWorkResultI> => {
  const result = await apiCall<OnboardingWorkBodyI, OnboardingWorkResultI>(
    '/backend/api/v1/user/profile/onboarding/work',
    'POST',
    { isAuth: true, payload },
  );

  return result;
};
