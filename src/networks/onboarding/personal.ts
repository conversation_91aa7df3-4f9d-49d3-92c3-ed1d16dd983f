/*
Copyright (c) 2025-present Navicater Solutions

This source code is licensed under the license found in the
LICENSE file in the root directory of this source tree.
*/
import { apiCall } from '../../services/api';
import { OnboardingPersonalBodyI, OnboardingPersonalResultI } from './types';

export const onboardingPersonalAPI = async (
  payload: OnboardingPersonalBodyI,
): Promise<OnboardingPersonalResultI> => {
  const result = await apiCall<OnboardingPersonalBodyI, OnboardingPersonalResultI>(
    '/backend/api/v1/user/profile/onboarding/personal',
    'POST',
    { isAuth: true, payload },
  );

  return result;
};
