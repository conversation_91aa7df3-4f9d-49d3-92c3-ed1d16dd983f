import { CargoCreateUpdatePayloadI } from '@/src/screens/EditCargoItem/components/EditCargoItem/types';
import { EquipmentCreateUpdatePayloadI } from '@/src/screens/EditEquipmentItem/components/EditEquipmentItem/types';
import { ShipCreateEditPayloadI } from '@/src/screens/EditShipItem/components/EditShipItem/types';
import { apiCall } from '../../services/api';
import {
  FetchShipCargosQueryI,
  FetchShipCargosResultI,
  fetchShipDetailsBodyI,
  fetchShipDetailsResultI,
  fetchSingleShipCargoResultI,
  fetchSingleShipDetailsResultI,
  fetchSingleShipEquipmentCategoryResultI,
} from './types';

export const fetchShipDetails = async (query: fetchShipDetailsBodyI) => {
  const result = await apiCall<unknown, fetchShipDetailsResultI>(
    '/backend/api/v1/ship/pre-populate',
    'GET',
    {
      isAuth: true,
      query,
    },
  );

  return result;
};

export const addShipForExperience = async (payload: ShipCreateEditPayloadI[]) => {
  const response = await apiCall<unknown, string[]>(
    '/backend/api/v1/career/profile-experience',
    'POST',
    {
      isAuth: true,
      payload,
    },
  );
  return response;
};

export const fetchSingleShipDetails = async (shipId: string) => {
  const response = await apiCall<unknown, fetchSingleShipDetailsResultI>(
    `/backend/api/v1/career/profile-experience/ship/${shipId}`,
    'GET',
    {
      isAuth: true,
    },
  );
  return response;
};

export const addEquipmentCategoryForShipExperience = async (
  payload: EquipmentCreateUpdatePayloadI,
) => {
  const response = await apiCall<unknown, string[]>(
    '/backend/api/v1/career/profile-experience',
    'POST',
    {
      isAuth: true,
      payload,
    },
  );
  return response;
};

export const addCargoForShipExperience = async (payload: CargoCreateUpdatePayloadI) => {
  const response = await apiCall<unknown, string[]>(
    '/backend/api/v1/career/profile-experience',
    'POST',
    {
      isAuth: true,
      payload,
    },
  );
  return response;
};

export const fetchSingleShipEquipmentCategory = async (equipmentId: string) => {
  const response = await apiCall<unknown, fetchSingleShipEquipmentCategoryResultI>(
    `/backend/api/v1/career/experience/equipment-category/${equipmentId}`,
    'GET',
    {
      isAuth: true,
    },
  );
  return response;
};

export const fetchSingleShipCargo = async (cargoId: string) => {
  const response = await apiCall<unknown, fetchSingleShipCargoResultI>(
    `/backend/api/v1/career/experience/cargo/${cargoId}`,
    'GET',
    {
      isAuth: true,
    },
  );
  return response;
};

export const fetchShipCargos = async (query: FetchShipCargosQueryI) => {
  const response = await apiCall<FetchShipCargosQueryI, FetchShipCargosResultI[]>(
    '/backend/api/v1/career/experience/cargos',
    'GET',
    {
      isAuth: true,
      query,
    },
  );
  return response;
};
