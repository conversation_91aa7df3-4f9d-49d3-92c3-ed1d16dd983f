import { apiCall } from '../../services/api';
import { PortVisitorFetchResultI, PortVisitQueryParams, TotalDataI } from './types';

export const fetchPortVisits = async (
  params: PortVisitQueryParams,
): Promise<TotalDataI<PortVisitorFetchResultI>> => {
  const { profileId, page = 0, pageSize = 10 } = params;

  const result = await apiCall<never, TotalDataI<PortVisitorFetchResultI>>(
    '/backend/api/v1/career/port-visits',
    'GET',
    {
      isAuth: true,
      query: {
        profileId,
        page: String(page),
        pageSize: String(pageSize),
      },
    },
  );

  return result;
};
