/*
Copyright (c) 2025-present Navicater Solutions

This source code is licensed under the license found in the
LICENSE file in the root directory of this source tree.
*/
import { apiCall } from '../../services/api';
import { FetchFollowersResponseI, FetchRequestsParamsI, RespondRequestParamsI } from './types';

export const fetchSentRequestsAPI = async ({
  pageSize = 10,
  page = 0,
}: FetchRequestsParamsI): Promise<FetchFollowersResponseI> => {
  const query = { pageSize, page } as FetchRequestsParamsI;
  const result = await apiCall<FetchRequestsParamsI, FetchFollowersResponseI>(
    '/backend/api/v1/network/request/sent',
    'GET',
    {
      isAuth: true,
      query,
    },
  );

  return result;
};

export const fetchReceivedRequestsAPI = async ({
  page = 0,
  pageSize = 10,
}: FetchRequestsParamsI): Promise<FetchFollowersResponseI> => {
  const query = { pageSize, page } as FetchRequestsParamsI;

  const result = await apiCall<FetchRequestsParamsI, FetchFollowersResponseI>(
    '/backend/api/v1/network/request/received',
    'GET',
    {
      isAuth: true,
      query,
    },
  );

  return result;
};

export const respondReceivedRequestAPI = async (
  payload: RespondRequestParamsI,
): Promise<unknown> => {
  const result = await apiCall<RespondRequestParamsI, unknown>(
    '/backend/api/v1/network/request/respond',
    'POST',
    {
      isAuth: true,
      payload,
    },
  );

  return result;
};
