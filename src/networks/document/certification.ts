import { apiCall } from '../../services/api';
import { AddCertificationBodyI, AddCertificationResultI } from './types';

export const addCertificationAPI = async (
  payload: AddCertificationBodyI,
): Promise<AddCertificationResultI> => {
  const result = await apiCall<AddCertificationBodyI, AddCertificationResultI>(
    '/backend/api/v1/career/profile-certificate',
    'POST',
    {
      isAuth: true,
      payload,
    },
  );

  return result;
};
