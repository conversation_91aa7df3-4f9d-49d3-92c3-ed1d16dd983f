import { apiCall } from '../../services/api';
import { AddDocumentBodyI, AddDocumentResultI } from './types';

export const addDocumentAPI = async (
  payload: AddDocumentBodyI,
  type: 'identity' | 'visa' | 'medical',
): Promise<AddDocumentResultI> => {
  const route = `/backend/api/v1/document/${type}`;
  const result = await apiCall<AddDocumentBodyI, AddDocumentResultI>(route, 'POST', {
    isAuth: true,
    payload,
  });

  return result;
};
