import { apiCall } from '../../services/api';
import { FetchPortVisitorQueryI, AddPortVisitorPayloadI, FetchVisitorsResult } from './types';

export const fetchPortVisitors = async (query: FetchPortVisitorQueryI) => {
  const result = await apiCall<unknown, FetchVisitorsResult>(
    '/backend/api/v1/port/scrap-book/visitors',
    'GET',
    {
      isAuth: true,
      query,
    },
  );

  return result;
};

export const addPortVisitor = async (payload: AddPortVisitorPayloadI) => {
  await apiCall<unknown, unknown>('/backend/api/v1/port/scrap-book/visitor', 'POST', {
    isAuth: true,
    payload,
  });
};

export const removePortVisitor = async (payload: AddPortVisitorPayloadI) => {
  await apiCall<unknown, unknown>('/backend/api/v1/port/scrap-book/visitor', 'DELETE', {
    isAuth: true,
    payload,
  });
};
