import type React from 'react';
import { useEffect } from 'react';
import { View, BackHandler } from 'react-native';
import Modal from 'react-native-modal';
import type { BottomSheetProps } from './types';

const BottomSheet: React.FC<BottomSheetProps> = ({
  visible,
  onClose,
  onModalHide,
  children,
  height,
  backdropOpacity = 0.5,
}) => {
  const sheetHeight = height ?? 500;

  useEffect(() => {
    const backHandler = BackHandler.addEventListener('hardwareBackPress', () => {
      if (visible) {
        onClose();
        return true;
      }
      return false;
    });

    return () => backHandler.remove();
  }, [visible, onClose]);

  return (
    <Modal
      isVisible={visible}
      backdropOpacity={backdropOpacity}
      onBackdropPress={onClose}
      onBackButtonPress={onClose}
      onModalHide={onModalHide}
      animationIn="slideInUp"
      animationOut="slideOutDown"
      animationInTiming={250}
      animationOutTiming={250}
      backdropTransitionInTiming={250}
      backdropTransitionOutTiming={1}
      statusBarTranslucent
      useNativeDriverForBackdrop
      hideModalContentWhileAnimating={false}
      avoidKeyboard
      style={{ margin: 0, justifyContent: 'flex-end' }}
    >
      <View
        className="bg-white rounded-t-[20px] shadow-lg w-full overflow-hidden"
        style={{ height: sheetHeight }}
      >
        <View className="items-center py-2.5">
          <View className="w-10 h-1.5 bg-gray-300 rounded-full" />
        </View>
        <View className="flex-1 px-5">{children}</View>
      </View>
    </Modal>
  );
};

export default BottomSheet;
