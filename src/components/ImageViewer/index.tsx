/*
Copyright (c) 2025-present Navicater Solutions

This source code is licensed under the license found in the
LICENSE file in the root directory of this source tree.
*/
import { Image, Text, TouchableOpacity, View } from 'react-native';
import Modal from 'react-native-modal';
import { useSafeAreaInsets } from 'react-native-safe-area-context';
import { formatSocialTime } from '@/src/utilities/datetime';
import Close from '@/src/assets/svgs/Close';
import { PostMediaI } from '@/src/networks/content/types';
import Carousel from '../Carousel';
import UserAvatar from '../UserAvatar';
import { ImageViewerModalProps } from './types';

const ImageViewer = ({ isVisible, onClose, post, initialIndex = 0 }: ImageViewerModalProps) => {
  const timeAgo = formatSocialTime(post.createdAt);
  const insets = useSafeAreaInsets();
  const hasSingleMedia = post.Media?.length === 1;

  const renderMediaItem = (item: PostMediaI, index: number) => {
    return (
      <View key={index} className="w-full h-full">
        <View className="w-full justify-center items-center">
          <Image source={{ uri: item.fileUrl }} className="w-full h-full" resizeMode="contain" />
        </View>
        {item.caption && (
          <View className="p-4 w-full">
            <Text className="text-white text-base">{item.caption}</Text>
          </View>
        )}
      </View>
    );
  };

  return (
    <View
      className="absolute pointer-events-none"
      style={{ top: insets.top, bottom: insets.bottom }}
    >
      <Modal
        isVisible={isVisible}
        onBackdropPress={onClose}
        onBackButtonPress={onClose}
        style={{ margin: 0 }}
        animationIn="fadeIn"
        animationOut="fadeOut"
        backdropOpacity={1}
        backdropColor="black"
        animationInTiming={250}
        animationOutTiming={250}
        backdropTransitionInTiming={250}
        backdropTransitionOutTiming={1}
        statusBarTranslucent
        useNativeDriverForBackdrop
        hideModalContentWhileAnimating={false}
        avoidKeyboard
      >
        <View className="flex-1 bg-black">
          <View className="h-full w-full" style={{ paddingTop: insets.top }}>
            <View className="flex-row items-center justify-between px-4">
              <View className="flex-row items-center flex-1">
                <UserAvatar
                  avatarUri={post.Profile.avatar}
                  name={post.Profile.name}
                  width={40}
                  height={40}
                  className="mr-3"
                />
                <View className="flex-1 ml-3">
                  <Text className="text-white font-bold text-base">{post.Profile.name}</Text>
                  <Text className="text-gray-300 text-xs" numberOfLines={1}>
                    {post.Profile.designation?.name}{' '}
                    {post.Profile.entity?.name ? `at ${post.Profile.entity.name}` : ''}
                  </Text>
                  <Text className="text-gray-400 text-xs">{timeAgo}</Text>
                </View>
              </View>
              <TouchableOpacity onPress={onClose} className="p-2 bg-white rounded-full">
                <Close stroke="white" width={2} height={2} />
              </TouchableOpacity>
            </View>
            <View className="flex-1 justify-center items-center">
              {hasSingleMedia ? (
                renderMediaItem(post.Media[0], 0)
              ) : (
                <Carousel
                  showArrows={true}
                  showDots={false}
                  showSlideNumbers={true}
                  activeColor="#FFFFFF"
                  inactiveColor="#FFFFFF50"
                  arrowClassName="bg-white/70 p-3 rounded-full"
                  dotClassName="h-3 w-3"
                  autoPlay={false}
                  initialIndex={initialIndex}
                >
                  {post.Media?.map((media, index) => (
                    <View className="w-full h-full" key={index}>
                      <Image
                        source={{ uri: media.fileUrl }}
                        className="w-full h-full"
                        resizeMode="contain"
                      />
                    </View>
                  ))}
                </Carousel>
              )}
            </View>
          </View>
        </View>
      </Modal>
    </View>
  );
};

export default ImageViewer;
