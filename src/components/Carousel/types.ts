/*
Copyright (c) 2025-present Navicater Solutions

This source code is licensed under the license found in the
LICENSE file in the root directory of this source tree.
*/
import { ReactNode } from 'react';

export interface CarouselProps {
  children: ReactNode[];
  showArrows?: boolean;
  showDots?: boolean;
  showSlideNumbers?: boolean;
  autoPlay?: boolean;
  duration?: number;
  className?: string;
  dotClassName?: string;
  arrowClassName?: string;
  activeColor?: string;
  inactiveColor?: string;
}
